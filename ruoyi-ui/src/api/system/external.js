import request from '@/utils/request'

// 查询外部系统列表
export function listExternal(query) {
  return request({
    url: '/system/external/list',
    method: 'get',
    params: query
  })
}

// 查询外部系统详细
export function getExternal(systemId) {
  return request({
    url: '/system/external/' + systemId,
    method: 'get'
  })
}

// 新增外部系统
export function addExternal(data) {
  return request({
    url: '/system/external',
    method: 'post',
    data: data
  })
}

// 修改外部系统
export function updateExternal(data) {
  return request({
    url: '/system/external',
    method: 'put',
    data: data
  })
}

// 删除外部系统
export function delExternal(systemId) {
  return request({
    url: '/system/external/' + systemId,
    method: 'delete'
  })
}

// 重新生成API密钥
export function regenerateApiKey(systemId) {
  return request({
    url: '/system/external/regenerateApiKey/' + systemId,
    method: 'post'
  })
}
