package com.ruoyi.common.utils.external;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.StringUtils;

/**
 * 外部API工具类
 * 
 * <AUTHOR>
 */
public class ExternalApiUtils
{
    private static final Logger log = LoggerFactory.getLogger(ExternalApiUtils.class);

    /**
     * 生成API请求签名
     * 
     * @param method HTTP方法
     * @param uri 请求URI
     * @param queryString 查询参数
     * @param timestamp 时间戳
     * @param apiSecret API密钥密文
     * @return 签名字符串
     */
    public static String generateSignature(String method, String uri, String queryString, String timestamp, String apiSecret)
    {
        try
        {
            // 构建签名字符串：HTTP方法 + URI + 查询参数 + 时间戳
            StringBuilder signString = new StringBuilder();
            signString.append(method.toUpperCase());
            signString.append(uri);
            
            if (StringUtils.isNotEmpty(queryString))
            {
                signString.append("?").append(queryString);
            }
            
            signString.append(timestamp);

            // 使用HMAC-SHA256生成签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signatureBytes = mac.doFinal(signString.toString().getBytes(StandardCharsets.UTF_8));
            
            // 转换为十六进制字符串
            return bytesToHex(signatureBytes);
        }
        catch (Exception e)
        {
            log.error("生成API签名时发生异常", e);
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 验证API请求签名
     * 
     * @param method HTTP方法
     * @param uri 请求URI
     * @param queryString 查询参数
     * @param timestamp 时间戳
     * @param signature 待验证的签名
     * @param apiSecret API密钥密文
     * @return 是否验证通过
     */
    public static boolean verifySignature(String method, String uri, String queryString, String timestamp, String signature, String apiSecret)
    {
        try
        {
            String expectedSignature = generateSignature(method, uri, queryString, timestamp, apiSecret);
            return signature.equals(expectedSignature);
        }
        catch (Exception e)
        {
            log.error("验证API签名时发生异常", e);
            return false;
        }
    }

    /**
     * 生成API密钥
     * 
     * @param systemCode 系统编码
     * @return API密钥
     */
    public static String generateApiKey(String systemCode)
    {
        try
        {
            String source = systemCode + System.currentTimeMillis();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(source.getBytes(StandardCharsets.UTF_8));
            
            return bytesToHex(hash).substring(0, 32); // 取前32位作为API Key
        }
        catch (Exception e)
        {
            log.error("生成API密钥时发生异常", e);
            throw new RuntimeException("API密钥生成失败", e);
        }
    }

    /**
     * 生成API密钥密文
     * 
     * @param systemCode 系统编码
     * @return API密钥密文
     */
    public static String generateApiSecret(String systemCode)
    {
        try
        {
            String source = systemCode + "SECRET" + System.currentTimeMillis();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(source.getBytes(StandardCharsets.UTF_8));
            
            return bytesToHex(hash); // 完整的64位作为Secret
        }
        catch (Exception e)
        {
            log.error("生成API密钥密文时发生异常", e);
            throw new RuntimeException("API密钥密文生成失败", e);
        }
    }

    /**
     * 生成API调用示例代码
     * 
     * @param apiKey API密钥
     * @param apiSecret API密钥密文
     * @param baseUrl 基础URL
     * @param endpoint 端点路径
     * @param method HTTP方法
     * @return 示例代码
     */
    public static String generateApiExample(String apiKey, String apiSecret, String baseUrl, String endpoint, String method)
    {
        long timestamp = System.currentTimeMillis() / 1000;
        String signature = generateSignature(method, endpoint, "", String.valueOf(timestamp), apiSecret);
        
        StringBuilder example = new StringBuilder();
        example.append("# API调用示例\n\n");
        
        // 请求头示例
        example.append("## 请求头\n");
        example.append("```\n");
        example.append("X-API-Key: ").append(apiKey).append("\n");
        example.append("X-Timestamp: ").append(timestamp).append("\n");
        example.append("X-Signature: ").append(signature).append("\n");
        example.append("Content-Type: application/json\n");
        example.append("```\n\n");
        
        // cURL示例
        example.append("## cURL示例\n");
        example.append("```bash\n");
        example.append("curl -X ").append(method.toUpperCase()).append(" \\\n");
        example.append("  '").append(baseUrl).append(endpoint).append("' \\\n");
        example.append("  -H 'X-API-Key: ").append(apiKey).append("' \\\n");
        example.append("  -H 'X-Timestamp: ").append(timestamp).append("' \\\n");
        example.append("  -H 'X-Signature: ").append(signature).append("' \\\n");
        example.append("  -H 'Content-Type: application/json'");
        
        if ("POST".equals(method.toUpperCase()) || "PUT".equals(method.toUpperCase()))
        {
            example.append(" \\\n");
            example.append("  -d '{\"key\": \"value\"}'");
        }
        
        example.append("\n```\n\n");
        
        // JavaScript示例
        example.append("## JavaScript示例\n");
        example.append("```javascript\n");
        example.append("const timestamp = Math.floor(Date.now() / 1000);\n");
        example.append("const signature = generateSignature('").append(method.toUpperCase()).append("', '").append(endpoint).append("', '', timestamp, '").append(apiSecret).append("');\n\n");
        example.append("fetch('").append(baseUrl).append(endpoint).append("', {\n");
        example.append("  method: '").append(method.toUpperCase()).append("',\n");
        example.append("  headers: {\n");
        example.append("    'X-API-Key': '").append(apiKey).append("',\n");
        example.append("    'X-Timestamp': timestamp,\n");
        example.append("    'X-Signature': signature,\n");
        example.append("    'Content-Type': 'application/json'\n");
        example.append("  }");
        
        if ("POST".equals(method.toUpperCase()) || "PUT".equals(method.toUpperCase()))
        {
            example.append(",\n");
            example.append("  body: JSON.stringify({key: 'value'})");
        }
        
        example.append("\n})\n");
        example.append(".then(response => response.json())\n");
        example.append(".then(data => console.log(data))\n");
        example.append(".catch(error => console.error('Error:', error));\n");
        example.append("```\n\n");
        
        // 签名算法说明
        example.append("## 签名算法\n");
        example.append("```\n");
        example.append("签名字符串 = HTTP方法 + URI + 查询参数 + 时间戳\n");
        example.append("签名 = HMAC-SHA256(签名字符串, API密钥密文)\n");
        example.append("```\n");
        
        return example.toString();
    }

    /**
     * 构建请求头Map
     * 
     * @param apiKey API密钥
     * @param timestamp 时间戳
     * @param signature 签名
     * @return 请求头Map
     */
    public static Map<String, String> buildHeaders(String apiKey, String timestamp, String signature)
    {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-Key", apiKey);
        headers.put("X-Timestamp", timestamp);
        headers.put("X-Signature", signature);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes)
    {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes)
        {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1)
            {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 验证时间戳是否在有效期内
     * 
     * @param timestamp 时间戳（秒）
     * @param validSeconds 有效期（秒）
     * @return 是否有效
     */
    public static boolean isTimestampValid(long timestamp, long validSeconds)
    {
        long currentTime = System.currentTimeMillis() / 1000;
        return Math.abs(currentTime - timestamp) <= validSeconds;
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小
     */
    public static String formatFileSize(long size)
    {
        if (size < 1024)
        {
            return size + " B";
        }
        else if (size < 1024 * 1024)
        {
            return String.format("%.1f KB", size / 1024.0);
        }
        else if (size < 1024 * 1024 * 1024)
        {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
        else
        {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 格式化持续时间
     * 
     * @param milliseconds 毫秒数
     * @return 格式化后的时间
     */
    public static String formatDuration(long milliseconds)
    {
        if (milliseconds < 1000)
        {
            return milliseconds + "ms";
        }
        else if (milliseconds < 60000)
        {
            return String.format("%.1fs", milliseconds / 1000.0);
        }
        else
        {
            long minutes = milliseconds / 60000;
            long seconds = (milliseconds % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }
}
