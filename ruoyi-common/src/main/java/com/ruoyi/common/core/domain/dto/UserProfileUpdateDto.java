package com.ruoyi.common.core.domain.dto;

import java.util.Date;
import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.xss.Xss;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户个人资料更新DTO
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@ApiModel(value = "UserProfileUpdateDto", description = "用户个人资料更新信息")
public class UserProfileUpdateDto
{
    /** 用户昵称 */
    @ApiModelProperty("用户昵称")
    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickName;

    /** 用户邮箱 */
    @ApiModelProperty("用户邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    private String email;

    /** 出生日期 */
    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /** 个人简介 */
    @ApiModelProperty("个人简介")
    @Xss(message = "个人简介不能包含脚本字符")
    @Size(min = 0, max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;

    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public Date getBirthDate()
    {
        return birthDate;
    }

    public void setBirthDate(Date birthDate)
    {
        this.birthDate = birthDate;
    }

    public String getBio()
    {
        return bio;
    }

    public void setBio(String bio)
    {
        this.bio = bio;
    }

    @Override
    public String toString()
    {
        return "UserProfileUpdateDto{" +
                "nickName='" + nickName + '\'' +
                ", email='" + email + '\'' +
                ", birthDate=" + birthDate +
                ", bio='" + bio + '\'' +
                '}';
    }
}
