-- 帮助文档系统数据库脚本

-- 创建帮助文档分类表
DROP TABLE IF EXISTS `help_category`;
CREATE TABLE `help_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `icon` varchar(200) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='帮助文档分类表';

-- 创建帮助文档表
DROP TABLE IF EXISTS `help_document`;
CREATE TABLE `help_document` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `title` varchar(200) NOT NULL COMMENT '文档标题',
  `content` text COMMENT '文档内容',
  `summary` varchar(500) DEFAULT NULL COMMENT '文档摘要',
  `keywords` varchar(200) DEFAULT NULL COMMENT '关键词（用于搜索）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `view_count` int(11) DEFAULT '0' COMMENT '查看次数',
  `is_hot` char(1) DEFAULT '0' COMMENT '是否热门（0否 1是）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_view_count` (`view_count`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='帮助文档表';

-- 插入示例分类数据
INSERT INTO `help_category` VALUES 
(1, '账户相关', 'user', 1, '0', 'admin', NOW(), NULL, NULL, '用户账户相关问题'),
(2, '功能使用', 'setting', 2, '0', 'admin', NOW(), NULL, NULL, '功能使用相关问题'),
(3, '技术支持', 'tool', 3, '0', 'admin', NOW(), NULL, NULL, '技术支持相关问题'),
(4, '常见问题', 'question', 4, '0', 'admin', NOW(), NULL, NULL, '常见问题解答');

-- 插入示例文档数据
INSERT INTO `help_document` VALUES 
(1, 1, '如何注册登录账户？', 
'<h2>注册流程</h2>
<p>1. 打开应用首页</p>
<p>2. 点击"注册"按钮</p>
<p>3. 填写手机号码</p>
<p>4. 获取验证码</p>
<p>5. 设置密码</p>
<p>6. 完成注册</p>

<h2>登录流程</h2>
<p>1. 打开应用首页</p>
<p>2. 点击"登录"按钮</p>
<p>3. 输入手机号和密码</p>
<p>4. 点击登录</p>', 
'详细介绍账户注册和登录流程', '注册,登录,账户', 1, 1250, '1', '0', 'admin', NOW(), NULL, NULL, NULL),

(2, 1, '忘记登录密码后如何找回？', 
'<h2>密码找回方式</h2>
<p>1. 通过手机号找回</p>
<p>2. 通过邮箱找回</p>
<p>3. 联系客服找回</p>

<h2>具体步骤</h2>
<p>1. 在登录页面点击"忘记密码"</p>
<p>2. 选择找回方式</p>
<p>3. 按照提示操作</p>', 
'密码找回的几种方式', '密码,找回,忘记', 2, 890, '1', '0', 'admin', NOW(), NULL, NULL, NULL),

(3, 2, '如何添加图片？', 
'<h2>图片上传步骤</h2>
<p>1. 进入素材管理页面</p>
<p>2. 点击"上传图片"按钮</p>
<p>3. 选择本地图片文件</p>
<p>4. 填写图片标题和描述</p>
<p>5. 选择分类标签</p>
<p>6. 点击确认上传</p>

<h2>支持格式</h2>
<p>支持JPG、PNG、GIF等常见格式</p>
<p>单个文件大小不超过10MB</p>', 
'图片上传和管理功能说明', '图片,上传,素材', 1, 756, '0', '0', 'admin', NOW(), NULL, NULL, NULL),

(4, 2, '如何投屏3d全息设备？', 
'<h2>设备连接</h2>
<p>1. 确保设备和手机在同一WiFi网络</p>
<p>2. 打开设备电源</p>
<p>3. 在应用中搜索设备</p>
<p>4. 点击连接</p>

<h2>投屏操作</h2>
<p>1. 选择要投屏的内容</p>
<p>2. 点击投屏按钮</p>
<p>3. 选择目标设备</p>
<p>4. 开始投屏</p>', 
'3D全息设备连接和投屏教程', '投屏,3D,全息,设备', 2, 623, '0', '0', 'admin', NOW(), NULL, NULL, NULL),

(5, 3, '遇到技术问题如何联系客服？', 
'<h2>联系方式</h2>
<p>1. 在线客服：应用内客服聊天</p>
<p>2. 客服电话：400-123-4567</p>
<p>3. 客服邮箱：<EMAIL></p>
<p>4. 工作时间：周一至周五 9:00-18:00</p>

<h2>反馈建议</h2>
<p>您也可以通过"意见反馈"功能提交问题</p>', 
'客服联系方式和反馈渠道', '客服,联系,技术支持', 1, 445, '0', '0', 'admin', NOW(), NULL, NULL, NULL);

-- 插入菜单权限数据
INSERT INTO sys_menu VALUES('2100', '帮助文档', '0', '8', 'help', NULL, NULL, '1', '0', 'M', '0', '0', '', 'documentation', 'admin', NOW(), '', NULL, '帮助文档管理菜单');
INSERT INTO sys_menu VALUES('2101', '分类管理', '2100', '1', 'category', 'help/category/index', NULL, '1', '0', 'C', '0', '0', 'system:help:list', 'tree', 'admin', NOW(), '', NULL, '帮助文档分类管理菜单');
INSERT INTO sys_menu VALUES('2102', '文档管理', '2100', '2', 'document', 'help/document/index', NULL, '1', '0', 'C', '0', '0', 'system:help:list', 'documentation', 'admin', NOW(), '', NULL, '帮助文档管理菜单');

-- 分类管理权限
INSERT INTO sys_menu VALUES('2103', '分类查询', '2101', '1', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2104', '分类新增', '2101', '2', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2105', '分类修改', '2101', '3', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2106', '分类删除', '2101', '4', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:remove', '#', 'admin', NOW(), '', NULL, '');

-- 文档管理权限
INSERT INTO sys_menu VALUES('2107', '文档查询', '2102', '1', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2108', '文档新增', '2102', '2', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2109', '文档修改', '2102', '3', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('2110', '文档删除', '2102', '4', '', '', NULL, '1', '0', 'F', '0', '0', 'system:help:remove', '#', 'admin', NOW(), '', NULL, '');
