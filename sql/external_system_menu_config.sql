-- ----------------------------
-- 外部系统管理菜单和配置数据
-- ----------------------------
USE cjviewer;

-- ----------------------------
-- 菜单权限配置
-- ----------------------------
-- 外部系统管理菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2100', '外部系统管理', '1', '8', 'external', NULL, NULL, 1, 0, 'M', '0', '0', '', 'international', 'admin', NOW(), '', NULL, '外部系统管理目录');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2101', '系统配置', '2100', '1', 'system', 'system/external/index', NULL, 1, 0, 'C', '0', '0', 'system:external:list', 'system', 'admin', NOW(), '', NULL, '外部系统配置菜单');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2102', '调用日志', '2100', '2', 'log', 'system/external/log', NULL, 1, 0, 'C', '0', '0', 'system:external:log', 'log', 'admin', NOW(), '', NULL, '外部API调用日志菜单');

-- 外部系统配置权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2103', '外部系统查询', '2101', '1', '', '', '', 1, 0, 'F', '0', '0', 'system:external:query', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2104', '外部系统新增', '2101', '2', '', '', '', 1, 0, 'F', '0', '0', 'system:external:add', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2105', '外部系统修改', '2101', '3', '', '', '', 1, 0, 'F', '0', '0', 'system:external:edit', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2106', '外部系统删除', '2101', '4', '', '', '', 1, 0, 'F', '0', '0', 'system:external:remove', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2107', '外部系统导出', '2101', '5', '', '', '', 1, 0, 'F', '0', '0', 'system:external:export', '', 'admin', NOW(), '', NULL, '');

-- 外部API日志权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2108', 'API日志查询', '2102', '1', '', '', '', 1, 0, 'F', '0', '0', 'system:external:log', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2109', 'API日志删除', '2102', '2', '', '', '', 1, 0, 'F', '0', '0', 'system:external:logRemove', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES('2110', 'API日志导出', '2102', '3', '', '', '', 1, 0, 'F', '0', '0', 'system:external:logExport', '', 'admin', NOW(), '', NULL, '');

-- ----------------------------
-- 字典配置
-- ----------------------------
-- 外部系统状态
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(100, '外部系统状态', 'external_system_status', '0', 'admin', NOW(), '', NULL, '外部系统状态列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(100, 1, '正常', '0', 'external_system_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '正常状态');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(101, 2, '停用', '1', 'external_system_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '停用状态');

-- API调用结果
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(101, 'API调用结果', 'api_call_result', '0', 'admin', NOW(), '', NULL, 'API调用结果状态');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(102, 1, '成功', '1', 'api_call_result', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '调用成功');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(103, 2, '失败', '0', 'api_call_result', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '调用失败');

-- 素材类型
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(102, '素材类型', 'material_type', '0', 'admin', NOW(), '', NULL, '灵感广场素材类型');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(104, 1, '图片', 'IMAGE', 'material_type', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '图片素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(105, 2, '视频', 'VIDEO', 'material_type', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '视频素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(106, 3, '音频', 'AUDIO', 'material_type', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '音频素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(107, 4, '文档', 'DOCUMENT', 'material_type', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '文档素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(108, 5, '其他', 'OTHER', 'material_type', '', 'default', 'N', '0', 'admin', NOW(), '', NULL, '其他素材');

-- ----------------------------
-- 配置参数
-- ----------------------------
INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(100, '外部API认证开关', 'external.api.auth.enabled', 'true', 'Y', 'admin', NOW(), '', NULL, '是否开启外部API认证（true开启，false关闭）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(101, '外部API签名有效期', 'external.api.signature.expire', '300', 'Y', 'admin', NOW(), '', NULL, '外部API请求签名有效期（秒）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(102, '外部API日志保留天数', 'external.api.log.retain.days', '30', 'Y', 'admin', NOW(), '', NULL, '外部API调用日志保留天数');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(103, '灵感广场系统地址', 'inspiration.square.base.url', 'http://localhost:9001', 'Y', 'admin', NOW(), '', NULL, '灵感广场系统基础URL地址');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(104, '外部API默认超时时间', 'external.api.default.timeout', '30', 'Y', 'admin', NOW(), '', NULL, '外部API默认超时时间（秒）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES(105, '外部API默认重试次数', 'external.api.default.retry', '3', 'Y', 'admin', NOW(), '', NULL, '外部API默认重试次数');
