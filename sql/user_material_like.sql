-- 用户素材点赞表
CREATE TABLE `user_material_like` (
  `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `material_id` varchar(100) NOT NULL COMMENT '素材ID（guiId）',
  `material_title` varchar(200) DEFAULT NULL COMMENT '素材标题',
  `material_tag` varchar(100) DEFAULT NULL COMMENT '素材标签',
  `photo_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `detail_url` varchar(500) DEFAULT NULL COMMENT '详情图URL',
  `like_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`like_id`),
  UNIQUE KEY `uk_user_material` (`user_id`, `material_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_material_tag` (`material_tag`),
  KEY `idx_like_time` (`like_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户素材点赞表';
