-- ----------------------------
-- 外部系统配置表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `external_system` (
  `system_id` bigint NOT NULL AUTO_INCREMENT COMMENT '系统ID',
  `system_name` varchar(50) NOT NULL COMMENT '系统名称',
  `system_code` varchar(30) NOT NULL COMMENT '系统编码',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `api_secret` varchar(128) NOT NULL COMMENT 'API密钥密文',
  `base_url` varchar(200) DEFAULT NULL COMMENT '系统基础URL',
  `timeout` int DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_count` int DEFAULT 3 COMMENT '重试次数',
  `allowed_ips` text COMMENT 'IP白名单，多个IP用逗号分隔',
  `rate_limit` int DEFAULT 100 COMMENT '限流配置(每分钟请求数)',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`system_id`),
  UNIQUE KEY `uk_system_code` (`system_code`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_system_name` (`system_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='外部系统配置表';

-- ----------------------------
-- 外部API调用日志表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `external_api_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `system_id` bigint DEFAULT NULL COMMENT '系统ID',
  `system_name` varchar(50) DEFAULT NULL COMMENT '系统名称',
  `request_url` varchar(500) NOT NULL COMMENT '请求URL',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_headers` text COMMENT '请求头',
  `request_params` text COMMENT '请求参数',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_headers` text COMMENT '响应头',
  `response_body` longtext COMMENT '响应内容',
  `duration` bigint DEFAULT NULL COMMENT '请求耗时(毫秒)',
  `request_ip` varchar(128) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `is_success` char(1) DEFAULT '0' COMMENT '是否成功（0失败 1成功）',
  `error_message` varchar(2000) DEFAULT NULL COMMENT '错误信息',
  `request_time` datetime NOT NULL COMMENT '请求时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_system_id` (`system_id`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_is_success` (`is_success`),
  KEY `idx_request_ip` (`request_ip`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='外部API调用日志表';

-- ----------------------------
-- 初始化外部系统配置数据
-- ----------------------------
INSERT IGNORE INTO `external_system` VALUES
(1, '灵感广场系统', 'INSPIRATION_SQUARE', 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6', 'q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6j7k8l9z0x1c2v3b4n5m6q7w8e9r0t1y2u3i4o5p6', 'http://localhost:9001', 30, 3, '127.0.0.1,***********/24', 1000, '0', '0', 'admin', NOW(), '', NULL, '灵感广场数据接口系统');

-- ----------------------------
-- 菜单权限配置
-- ----------------------------
-- 外部系统管理菜单
-- ----------------------------
-- 外部系统管理菜单和配置数据
-- ----------------------------
USE cjviewer;

-- ----------------------------
-- 菜单权限配置
-- ----------------------------
-- 外部系统管理菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2100', '外部系统管理', '1', '8', 'external', NULL, NULL, 1, 0, 'M', '0', '0', '', 'international', 'admin', NOW(), '', NULL, '外部系统管理目录');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2101', '系统配置', '2100', '1', 'system', 'system/external/index', NULL, 1, 0, 'C', '0', '0', 'system:external:list', 'system', 'admin', NOW(), '', NULL, '外部系统配置菜单');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2102', '调用日志', '2100', '2', 'log', 'system/external/log', NULL, 1, 0, 'C', '0', '0', 'system:external:log', 'log', 'admin', NOW(), '', NULL, '外部API调用日志菜单');

-- 外部系统配置权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2103', '外部系统查询', '2101', '1', '', '', '', 1, 0, 'F', '0', '0', 'system:external:query', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2104', '外部系统新增', '2101', '2', '', '', '', 1, 0, 'F', '0', '0', 'system:external:add', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2105', '外部系统修改', '2101', '3', '', '', '', 1, 0, 'F', '0', '0', 'system:external:edit', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2106', '外部系统删除', '2101', '4', '', '', '', 1, 0, 'F', '0', '0', 'system:external:remove', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2107', '外部系统导出', '2101', '5', '', '', '', 1, 0, 'F', '0', '0', 'system:external:export', '', 'admin', NOW(), '', NULL, '');

-- 外部API日志权限
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2108', 'API日志查询', '2102', '1', '', '', '', 1, 0, 'F', '0', '0', 'system:external:log', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2109', 'API日志删除', '2102', '2', '', '', '', 1, 0, 'F', '0', '0', 'system:external:logRemove', '', 'admin', NOW(), '', NULL, '');

INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('2110', 'API日志导出', '2102', '3', '', '', '', 1, 0, 'F', '0', '0', 'system:external:logExport', '', 'admin', NOW(), '', NULL, '');

-- ----------------------------
-- 字典配置
-- ----------------------------
-- 外部系统状态
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(100, '外部系统状态', 'external_system_status', '0', 'admin', NOW(), '', NULL, '外部系统状态列表');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(100, 1, '正常', '0', 'external_system_status', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '正常状态');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(101, 2, '停用', '1', 'external_system_status', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '停用状态');

-- API调用结果
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(101, 'API调用结果', 'api_call_result', '0', 'admin', NOW(), '', NULL, 'API调用结果状态');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(102, 1, '成功', '1', 'api_call_result', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '调用成功');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(103, 2, '失败', '0', 'api_call_result', '', 'danger', 'N', '0', 'admin', NOW(), '', NULL, '调用失败');

-- 素材类型
INSERT IGNORE INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(102, '素材类型', 'material_type', '0', 'admin', NOW(), '', NULL, '灵感广场素材类型');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(104, 1, '图片', 'IMAGE', 'material_type', '', 'primary', 'N', '0', 'admin', NOW(), '', NULL, '图片素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(105, 2, '视频', 'VIDEO', 'material_type', '', 'success', 'N', '0', 'admin', NOW(), '', NULL, '视频素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(106, 3, '音频', 'AUDIO', 'material_type', '', 'info', 'N', '0', 'admin', NOW(), '', NULL, '音频素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(107, 4, '文档', 'DOCUMENT', 'material_type', '', 'warning', 'N', '0', 'admin', NOW(), '', NULL, '文档素材');

INSERT IGNORE INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(108, 5, '其他', 'OTHER', 'material_type', '', 'default', 'N', '0', 'admin', NOW(), '', NULL, '其他素材');

-- ----------------------------
-- 配置参数
-- ----------------------------
INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(100, '外部API认证开关', 'external.api.auth.enabled', 'true', 'Y', 'admin', NOW(), '', NULL, '是否开启外部API认证（true开启，false关闭）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(101, '外部API签名有效期', 'external.api.signature.expire', '300', 'Y', 'admin', NOW(), '', NULL, '外部API请求签名有效期（秒）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(102, '外部API日志保留天数', 'external.api.log.retain.days', '30', 'Y', 'admin', NOW(), '', NULL, '外部API调用日志保留天数');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(103, '灵感广场系统地址', 'inspiration.square.base.url', 'http://localhost:9001', 'Y', 'admin', NOW(), '', NULL, '灵感广场系统基础URL地址');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(104, '外部API默认超时时间', 'external.api.default.timeout', '30', 'Y', 'admin', NOW(), '', NULL, '外部API默认超时时间（秒）');

INSERT IGNORE INTO sys_config (config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(105, '外部API默认重试次数', 'external.api.default.retry', '3', 'Y', 'admin', NOW(), '', NULL, '外部API默认重试次数');
