-- ----------------------------
-- 外部系统核心表结构
-- ----------------------------
<PERSON><PERSON> cjviewer;

-- ----------------------------
-- 外部系统配置表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `external_system` (
  `system_id` bigint NOT NULL AUTO_INCREMENT COMMENT '系统ID',
  `system_name` varchar(50) NOT NULL COMMENT '系统名称',
  `system_code` varchar(30) NOT NULL COMMENT '系统编码',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `api_secret` varchar(128) NOT NULL COMMENT 'API密钥密文',
  `base_url` varchar(200) DEFAULT NULL COMMENT '系统基础URL',
  `timeout` int DEFAULT 30 COMMENT '超时时间(秒)',
  `retry_count` int DEFAULT 3 COMMENT '重试次数',
  `allowed_ips` text COMMENT 'IP白名单，多个IP用逗号分隔',
  `rate_limit` int DEFAULT 100 COMMENT '限流配置(每分钟请求数)',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`system_id`),
  UNIQUE KEY `uk_system_code` (`system_code`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_system_name` (`system_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='外部系统配置表';

-- ----------------------------
-- 外部API调用日志表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `external_api_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `system_id` bigint DEFAULT NULL COMMENT '系统ID',
  `system_name` varchar(50) DEFAULT NULL COMMENT '系统名称',
  `request_url` varchar(500) NOT NULL COMMENT '请求URL',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_headers` text COMMENT '请求头',
  `request_params` text COMMENT '请求参数',
  `response_status` int DEFAULT NULL COMMENT '响应状态码',
  `response_headers` text COMMENT '响应头',
  `response_body` longtext COMMENT '响应内容',
  `duration` bigint DEFAULT NULL COMMENT '请求耗时(毫秒)',
  `request_ip` varchar(128) DEFAULT NULL COMMENT '请求IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `is_success` char(1) DEFAULT '0' COMMENT '是否成功（0失败 1成功）',
  `error_message` varchar(2000) DEFAULT NULL COMMENT '错误信息',
  `request_time` datetime NOT NULL COMMENT '请求时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_system_id` (`system_id`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_is_success` (`is_success`),
  KEY `idx_request_ip` (`request_ip`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='外部API调用日志表';

-- ----------------------------
-- 初始化外部系统配置数据
-- ----------------------------
INSERT IGNORE INTO `external_system` (system_id, system_name, system_code, api_key, api_secret, base_url, timeout, retry_count, allowed_ips, rate_limit, status, del_flag, create_by, create_time, update_by, update_time, remark) 
VALUES (1, '灵感广场系统', 'INSPIRATION_SQUARE', 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6', 'q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6j7k8l9z0x1c2v3b4n5m6q7w8e9r0t1y2u3i4o5p6', 'http://localhost:9001', 30, 3, '127.0.0.1,***********/24', 1000, '0', '0', 'admin', NOW(), '', NULL, '灵感广场数据接口系统');

-- ----------------------------
-- 索引优化
-- ----------------------------
-- 为外部API日志表添加复合索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() AND table_name = 'external_api_log' AND index_name = 'idx_system_time') > 0,
    'SELECT ''Index idx_system_time already exists'' as message',
    'ALTER TABLE `external_api_log` ADD INDEX `idx_system_time` (`system_id`, `request_time`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() AND table_name = 'external_api_log' AND index_name = 'idx_success_time') > 0,
    'SELECT ''Index idx_success_time already exists'' as message',
    'ALTER TABLE `external_api_log` ADD INDEX `idx_success_time` (`is_success`, `request_time`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为外部系统表添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() AND table_name = 'external_system' AND index_name = 'idx_status_time') > 0,
    'SELECT ''Index idx_status_time already exists'' as message',
    'ALTER TABLE `external_system` ADD INDEX `idx_status_time` (`status`, `create_time`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表创建结果
SELECT 'external_system table created successfully' as message;
SELECT 'external_api_log table created successfully' as message;
SELECT 'Initial data inserted successfully' as message;
