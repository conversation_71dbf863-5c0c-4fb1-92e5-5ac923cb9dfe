package com.ruoyi.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.IExternalApiLogService;
import com.ruoyi.system.service.ISysConfigService;

/**
 * 外部API日志清理定时任务
 * 
 * <AUTHOR>
 */
@Component("externalApiLogCleanTask")
public class ExternalApiLogCleanTask
{
    private static final Logger log = LoggerFactory.getLogger(ExternalApiLogCleanTask.class);

    @Autowired
    private IExternalApiLogService externalApiLogService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 清理过期的外部API调用日志
     */
    public void cleanExpiredLogs()
    {
        cleanExpiredLogs(null);
    }

    /**
     * 清理过期的外部API调用日志
     * 
     * @param params 参数（保留天数）
     */
    public void cleanExpiredLogs(String params)
    {
        try
        {
            // 获取保留天数配置
            int retainDays = getRetainDays(params);
            
            if (retainDays <= 0)
            {
                log.warn("外部API日志保留天数配置无效，跳过清理任务");
                return;
            }

            log.info("开始清理{}天前的外部API调用日志", retainDays);
            
            // 执行清理
            int cleanedCount = externalApiLogService.cleanExternalApiLogByDays(retainDays);
            
            log.info("外部API日志清理完成，共清理{}条记录", cleanedCount);
        }
        catch (Exception e)
        {
            log.error("清理外部API日志时发生异常", e);
        }
    }

    /**
     * 获取日志保留天数
     * 
     * @param params 任务参数
     * @return 保留天数
     */
    private int getRetainDays(String params)
    {
        int retainDays = 30; // 默认保留30天
        
        try
        {
            // 优先使用任务参数
            if (StringUtils.isNotEmpty(params))
            {
                retainDays = Integer.parseInt(params.trim());
            }
            else
            {
                // 从系统配置中获取
                String configValue = configService.selectConfigByKey("external.api.log.retain.days");
                if (StringUtils.isNotEmpty(configValue))
                {
                    retainDays = Integer.parseInt(configValue);
                }
            }
        }
        catch (NumberFormatException e)
        {
            log.warn("解析日志保留天数配置失败，使用默认值30天", e);
            retainDays = 30;
        }
        
        return retainDays;
    }

    /**
     * 统计外部API调用情况
     */
    public void statisticsApiCalls()
    {
        statisticsApiCalls(null);
    }

    /**
     * 统计外部API调用情况
     * 
     * @param params 参数（统计天数）
     */
    public void statisticsApiCalls(String params)
    {
        try
        {
            int statisticsDays = 7; // 默认统计最近7天
            
            if (StringUtils.isNotEmpty(params))
            {
                try
                {
                    statisticsDays = Integer.parseInt(params.trim());
                }
                catch (NumberFormatException e)
                {
                    log.warn("解析统计天数参数失败，使用默认值7天", e);
                }
            }

            log.info("开始统计最近{}天的外部API调用情况", statisticsDays);
            
            // 这里可以添加具体的统计逻辑
            // 例如：统计调用次数、成功率、平均响应时间等
            // 并将统计结果存储到数据库或发送报告
            
            log.info("外部API调用统计完成");
        }
        catch (Exception e)
        {
            log.error("统计外部API调用情况时发生异常", e);
        }
    }

    /**
     * 检查外部系统健康状态
     */
    public void checkExternalSystemHealth()
    {
        checkExternalSystemHealth(null);
    }

    /**
     * 检查外部系统健康状态
     * 
     * @param params 参数
     */
    public void checkExternalSystemHealth(String params)
    {
        try
        {
            log.info("开始检查外部系统健康状态");
            
            // 这里可以添加健康检查逻辑
            // 例如：
            // 1. 检查各外部系统的连通性
            // 2. 检查API调用成功率
            // 3. 检查响应时间是否正常
            // 4. 发送告警通知（如果有异常）
            
            log.info("外部系统健康状态检查完成");
        }
        catch (Exception e)
        {
            log.error("检查外部系统健康状态时发生异常", e);
        }
    }
}
