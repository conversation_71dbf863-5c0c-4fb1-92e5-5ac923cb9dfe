package com.ruoyi.web.controller.material.service;

import java.util.List;
import java.util.Map;

/**
 * 素材数据服务接口
 * 
 * <AUTHOR>
 */
public interface MaterialDataService
{
    /**
     * 获取用户的个人创作素材
     * 
     * @param userId 用户ID
     * @return 素材列表
     */
    List<Map<String, Object>> getPersonalMaterials(Long userId);

    /**
     * 获取灵感广场素材
     * 
     * @return 素材列表
     */
    List<Map<String, Object>> getInspirationMaterials();
}
