package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.controller.external.service.InspirationSquareService;
import com.ruoyi.system.service.IUserMaterialLikeService;
import com.ruoyi.system.domain.UserMaterialLike;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
@Api("素材广场数据接口")
@RestController
@RequestMapping("/inspiration")
public class InspirationController extends BaseController {

    @Autowired
    private InspirationSquareService inspirationSquareService;

    @Autowired
    private IUserMaterialLikeService userMaterialLikeService;

    @ApiOperation("获取素材分类列表")
    @GetMapping("/GetAllContentDetail")
    public AjaxResult getMaterials(@RequestParam(required = false) String userId,@RequestParam(required = false) String tag) {
        try {
            // 使用默认token调用外部系统
            String defaultToken = "system_token";
            List<Map<String, Object>> materials = inspirationSquareService.getInspirationMaterials(defaultToken, userId != null ? userId : "admin",tag);
            return AjaxResult.success(materials);
        } catch (Exception e) {
            return AjaxResult.error("获取素材失败: " + e.getMessage());
        }
    }

    /**
     * 上传图片文件到素材广场
     */
    @ApiOperation("上传文件到素材广场")
    @PostMapping("/upload")
    public AjaxResult uploadMaterial(
            @ApiParam("图片文件大小不能超过10MB") @RequestParam("file") MultipartFile file,
            @ApiParam("素材标题") @RequestParam(value = "title", required = false) String title,
            @ApiParam("类型") @RequestParam(value = "type", required = true) String type,
            @ApiParam("素材标签") @RequestParam(value = "tag", required = true) String tag,
            @ApiParam("发布状态：draft-草稿，published-已发布") @RequestParam(value = "status", defaultValue = "draft") String status,
            HttpServletRequest request) {

        try {
            // 调用服务层上传文件到素材广场
            Map<String, Object> result = inspirationSquareService.uploadMaterial(file, title, type, tag, status, request);
            return AjaxResult.success("文件上传成功", result);
        } catch (Exception e) {
            logger.error("上传文件到素材广场失败", e);
            return AjaxResult.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传模型文件到素材广场
     */
    @ApiOperation("上传模型到素材广场")
    @PostMapping("/uploadModel")
    public AjaxResult uploadModel(
            @ApiParam("模型预览图片（必填，大小不能超过10MB）") @RequestParam("file") MultipartFile file,
            @ApiParam("模型文件（必填，支持.glb/.gltf/.obj等格式，大小不能超过50MB）") @RequestParam("modelFile") MultipartFile modelFile,
            @ApiParam("素材标题") @RequestParam(value = "title", required = false) String title,
            @ApiParam("素材描述") @RequestParam(value = "description", required = false) String description,
            @ApiParam("素材标签") @RequestParam(value = "tag", required = false) String tag,
            @ApiParam("发布状态：draft-草稿，published-已发布") @RequestParam(value = "status", defaultValue = "draft") String status,
            HttpServletRequest request) {

        try {
            // 调用服务层上传模型文件到素材广场
            Map<String, Object> result = inspirationSquareService.uploadModel(file, modelFile, title, description, tag, status, request);
            return AjaxResult.success("模型上传成功", result);
        } catch (Exception e) {
            logger.error("上传模型到素材广场失败", e);
            return AjaxResult.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 点赞/取消点赞素材
     */
    @ApiOperation("点赞/取消点赞素材")
    @PostMapping("/like")
    @PreAuthorize("@ss.hasPermi('system:inspiration:like')")
    public AjaxResult toggleLike(
            @ApiParam("素材ID（guiId）") @RequestParam("materialId") String materialId,
            @ApiParam("操作类型：like-点赞，unlike-取消点赞") @RequestParam("action") String action,
            @ApiParam("素材标题") @RequestParam(value = "title", required = false) String title,
            @ApiParam("素材标签") @RequestParam(value = "tag", required = false) String tag,
            @ApiParam("缩略图URL") @RequestParam(value = "photoUrl", required = false) String photoUrl,
            @ApiParam("详情图URL") @RequestParam(value = "detailUrl", required = false) String detailUrl,
            HttpServletRequest request) {

        try {
            Long userId = SecurityUtils.getUserId();
            Map<String, Object> result = userMaterialLikeService.toggleLike(userId, materialId, action, title, tag, photoUrl, detailUrl);

            if ("like".equals(action)) {
                return AjaxResult.success("点赞成功", result);
            } else {
                return AjaxResult.success("取消点赞成功", result);
            }
        } catch (Exception e) {
            logger.error("点赞操作失败", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 查询我的喜欢列表
     */
    @ApiOperation("查询我的喜欢列表")
    @GetMapping("/likes")
    @PreAuthorize("@ss.hasPermi('system:inspiration:view')")
    public TableDataInfo getMyLikes(
            @ApiParam("素材标签筛选") @RequestParam(value = "tag", required = false) String tag,
            HttpServletRequest request) {

        try {
            Long userId = SecurityUtils.getUserId();
            startPage();
            List<UserMaterialLike> list = userMaterialLikeService.selectUserLikes(userId, tag);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询我的喜欢失败", e);
            return getDataTable(new java.util.ArrayList<>());
        }
    }

    /**
     * 批量查询素材点赞状态
     */
    @ApiOperation("批量查询素材点赞状态")
    @PostMapping("/likes/batch")
    @PreAuthorize("@ss.hasPermi('system:inspiration:view')")
    public AjaxResult batchCheckLikeStatus(
            @ApiParam("素材ID列表") @org.springframework.web.bind.annotation.RequestBody List<String> materialIds,
            HttpServletRequest request) {

        try {
            Long userId = SecurityUtils.getUserId();
            Map<String, Boolean> result = userMaterialLikeService.batchCheckLikeStatus(userId, materialIds);
            return AjaxResult.success("查询成功", result);
        } catch (Exception e) {
            logger.error("批量查询点赞状态失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 下载素材文件
     */
    @ApiOperation("下载素材文件")
    @GetMapping("/download/{materialId}")
    @PreAuthorize("@ss.hasPermi('system:inspiration:view')")
    public void downloadMaterial(
            @ApiParam("素材ID") @PathVariable String materialId,
            @ApiParam("文件类型：original-原图，thumbnail-缩略图，model-模型文件") @RequestParam(defaultValue = "original") String type,
            HttpServletRequest request,
            HttpServletResponse response) {

        try {
            // 调用服务层下载文件
            inspirationSquareService.downloadMaterial(materialId, type, response);
        } catch (Exception e) {
            logger.error("下载素材文件失败，素材ID: {}, 类型: {}", materialId, type, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"下载失败: " + e.getMessage() + "\"}");
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 更新素材发布状态
     */
    @ApiOperation("更新素材发布状态")
    @PostMapping("/updateStatus")
    @PreAuthorize("@ss.hasPermi('system:inspiration:edit')")
    public AjaxResult updateMaterialStatus(
            @ApiParam("素材ID") @RequestParam("materialId") String materialId,
            @ApiParam("发布状态：draft-草稿，published-已发布") @RequestParam("status") String status,
            HttpServletRequest request) {

        try {
            // 调用服务层更新状态
            boolean result = inspirationSquareService.updateMaterialStatus(materialId, status, request);
            if (result) {
                return AjaxResult.success("状态更新成功");
            } else {
                return AjaxResult.error("状态更新失败");
            }
        } catch (Exception e) {
            logger.error("更新素材状态失败，素材ID: {}, 状态: {}", materialId, status, e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }
}
