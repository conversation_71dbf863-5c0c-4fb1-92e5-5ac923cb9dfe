package com.ruoyi.web.controller.external.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 灵感广场素材对象
 * 
 * <AUTHOR>
 */
@ApiModel("灵感广场素材")
public class InspirationMaterial
{
    /** 素材ID */
    @ApiModelProperty("素材ID")
    private Long materialId;

    /** 素材标题 */
    @ApiModelProperty("素材标题")
    private String title;

    /** 素材描述 */
    @ApiModelProperty("素材描述")
    private String description;

    /** 素材类型 */
    @ApiModelProperty("素材类型")
    private String materialType;

    /** 分类ID */
    @ApiModelProperty("分类ID")
    private Long categoryId;

    /** 分类名称 */
    @ApiModelProperty("分类名称")
    private String categoryName;

    /** 素材内容 */
    @ApiModelProperty("素材内容")
    private String content;

    /** 素材URL */
    @ApiModelProperty("素材URL")
    private String materialUrl;

    /** 缩略图URL */
    @ApiModelProperty("缩略图URL")
    private String thumbnailUrl;

    /** 文件大小 */
    @ApiModelProperty("文件大小")
    private Long fileSize;

    /** 文件格式 */
    @ApiModelProperty("文件格式")
    private String fileFormat;

    /** 分辨率 */
    @ApiModelProperty("分辨率")
    private String resolution;

    /** 时长（视频/音频） */
    @ApiModelProperty("时长")
    private Integer duration;

    /** 标签列表 */
    @ApiModelProperty("标签列表")
    private List<String> tags;

    /** 作者 */
    @ApiModelProperty("作者")
    private String author;

    /** 来源 */
    @ApiModelProperty("来源")
    private String source;

    /** 版权信息 */
    @ApiModelProperty("版权信息")
    private String copyright;

    /** 使用许可 */
    @ApiModelProperty("使用许可")
    private String license;

    /** 价格 */
    @ApiModelProperty("价格")
    private Double price;

    /** 是否免费 */
    @ApiModelProperty("是否免费")
    private Boolean isFree;

    /** 下载次数 */
    @ApiModelProperty("下载次数")
    private Long downloadCount;

    /** 浏览次数 */
    @ApiModelProperty("浏览次数")
    private Long viewCount;

    /** 收藏次数 */
    @ApiModelProperty("收藏次数")
    private Long favoriteCount;

    /** 评分 */
    @ApiModelProperty("评分")
    private Double rating;

    /** 评价数量 */
    @ApiModelProperty("评价数量")
    private Long reviewCount;

    /** 状态 */
    @ApiModelProperty("状态")
    private String status;

    /** 是否推荐 */
    @ApiModelProperty("是否推荐")
    private Boolean isRecommended;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 发布时间 */
    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 扩展属性 */
    @ApiModelProperty("扩展属性")
    private Object extendedProperties;

    public Long getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(Long materialId)
    {
        this.materialId = materialId;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getDescription()
    {
        return description;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(String materialType)
    {
        this.materialType = materialType;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getContent()
    {
        return content;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getMaterialUrl()
    {
        return materialUrl;
    }

    public void setMaterialUrl(String materialUrl)
    {
        this.materialUrl = materialUrl;
    }

    public String getThumbnailUrl()
    {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl)
    {
        this.thumbnailUrl = thumbnailUrl;
    }

    public Long getFileSize()
    {
        return fileSize;
    }

    public void setFileSize(Long fileSize)
    {
        this.fileSize = fileSize;
    }

    public String getFileFormat()
    {
        return fileFormat;
    }

    public void setFileFormat(String fileFormat)
    {
        this.fileFormat = fileFormat;
    }

    public String getResolution()
    {
        return resolution;
    }

    public void setResolution(String resolution)
    {
        this.resolution = resolution;
    }

    public Integer getDuration()
    {
        return duration;
    }

    public void setDuration(Integer duration)
    {
        this.duration = duration;
    }

    public List<String> getTags()
    {
        return tags;
    }

    public void setTags(List<String> tags)
    {
        this.tags = tags;
    }

    public String getAuthor()
    {
        return author;
    }

    public void setAuthor(String author)
    {
        this.author = author;
    }

    public String getSource()
    {
        return source;
    }

    public void setSource(String source)
    {
        this.source = source;
    }

    public String getCopyright()
    {
        return copyright;
    }

    public void setCopyright(String copyright)
    {
        this.copyright = copyright;
    }

    public String getLicense()
    {
        return license;
    }

    public void setLicense(String license)
    {
        this.license = license;
    }

    public Double getPrice()
    {
        return price;
    }

    public void setPrice(Double price)
    {
        this.price = price;
    }

    public Boolean getIsFree()
    {
        return isFree;
    }

    public void setIsFree(Boolean isFree)
    {
        this.isFree = isFree;
    }

    public Long getDownloadCount()
    {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount)
    {
        this.downloadCount = downloadCount;
    }

    public Long getViewCount()
    {
        return viewCount;
    }

    public void setViewCount(Long viewCount)
    {
        this.viewCount = viewCount;
    }

    public Long getFavoriteCount()
    {
        return favoriteCount;
    }

    public void setFavoriteCount(Long favoriteCount)
    {
        this.favoriteCount = favoriteCount;
    }

    public Double getRating()
    {
        return rating;
    }

    public void setRating(Double rating)
    {
        this.rating = rating;
    }

    public Long getReviewCount()
    {
        return reviewCount;
    }

    public void setReviewCount(Long reviewCount)
    {
        this.reviewCount = reviewCount;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Boolean getIsRecommended()
    {
        return isRecommended;
    }

    public void setIsRecommended(Boolean isRecommended)
    {
        this.isRecommended = isRecommended;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public Date getPublishTime()
    {
        return publishTime;
    }

    public void setPublishTime(Date publishTime)
    {
        this.publishTime = publishTime;
    }

    public Object getExtendedProperties()
    {
        return extendedProperties;
    }

    public void setExtendedProperties(Object extendedProperties)
    {
        this.extendedProperties = extendedProperties;
    }

    @Override
    public String toString()
    {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("materialId", getMaterialId())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("materialType", getMaterialType())
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("content", getContent())
            .append("materialUrl", getMaterialUrl())
            .append("thumbnailUrl", getThumbnailUrl())
            .append("fileSize", getFileSize())
            .append("fileFormat", getFileFormat())
            .append("resolution", getResolution())
            .append("duration", getDuration())
            .append("tags", getTags())
            .append("author", getAuthor())
            .append("source", getSource())
            .append("copyright", getCopyright())
            .append("license", getLicense())
            .append("price", getPrice())
            .append("isFree", getIsFree())
            .append("downloadCount", getDownloadCount())
            .append("viewCount", getViewCount())
            .append("favoriteCount", getFavoriteCount())
            .append("rating", getRating())
            .append("reviewCount", getReviewCount())
            .append("status", getStatus())
            .append("isRecommended", getIsRecommended())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("publishTime", getPublishTime())
            .append("extendedProperties", getExtendedProperties())
            .toString();
    }
}
