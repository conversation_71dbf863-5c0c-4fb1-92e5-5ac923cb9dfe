package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ExternalSystem;
import com.ruoyi.system.service.IExternalSystemService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 外部系统配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api("外部系统配置管理")
@RestController
@RequestMapping("/system/external")
public class SysExternalSystemController extends BaseController
{
    @Autowired
    private IExternalSystemService externalSystemService;

    /**
     * 查询外部系统配置列表
     */
    @ApiOperation("查询外部系统配置列表")
    @PreAuthorize("@ss.hasPermi('system:external:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExternalSystem externalSystem)
    {
        startPage();
        List<ExternalSystem> list = externalSystemService.selectExternalSystemList(externalSystem);
        return getDataTable(list);
    }

    /**
     * 导出外部系统配置列表
     */
    @ApiOperation("导出外部系统配置列表")
    @PreAuthorize("@ss.hasPermi('system:external:export')")
    @Log(title = "外部系统配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExternalSystem externalSystem)
    {
        List<ExternalSystem> list = externalSystemService.selectExternalSystemList(externalSystem);
        ExcelUtil<ExternalSystem> util = new ExcelUtil<ExternalSystem>(ExternalSystem.class);
        util.exportExcel(response, list, "外部系统配置数据");
    }

    /**
     * 获取外部系统配置详细信息
     */
    @ApiOperation("获取外部系统配置详细信息")
    @PreAuthorize("@ss.hasPermi('system:external:query')")
    @GetMapping(value = "/{systemId}")
    public AjaxResult getInfo(@ApiParam("系统ID") @PathVariable("systemId") Long systemId)
    {
        return success(externalSystemService.selectExternalSystemBySystemId(systemId));
    }

    /**
     * 新增外部系统配置
     */
    @ApiOperation("新增外部系统配置")
    @PreAuthorize("@ss.hasPermi('system:external:add')")
    @Log(title = "外部系统配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExternalSystem externalSystem)
    {
        if (!externalSystemService.checkSystemCodeUnique(externalSystem))
        {
            return error("新增外部系统'" + externalSystem.getSystemName() + "'失败，系统编码已存在");
        }
        else if (!externalSystemService.checkSystemNameUnique(externalSystem))
        {
            return error("新增外部系统'" + externalSystem.getSystemName() + "'失败，系统名称已存在");
        }
        externalSystem.setCreateBy(getUsername());
        return toAjax(externalSystemService.insertExternalSystem(externalSystem));
    }

    /**
     * 修改外部系统配置
     */
    @ApiOperation("修改外部系统配置")
    @PreAuthorize("@ss.hasPermi('system:external:edit')")
    @Log(title = "外部系统配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExternalSystem externalSystem)
    {
        if (!externalSystemService.checkSystemCodeUnique(externalSystem))
        {
            return error("修改外部系统'" + externalSystem.getSystemName() + "'失败，系统编码已存在");
        }
        else if (!externalSystemService.checkSystemNameUnique(externalSystem))
        {
            return error("修改外部系统'" + externalSystem.getSystemName() + "'失败，系统名称已存在");
        }
        externalSystem.setUpdateBy(getUsername());
        return toAjax(externalSystemService.updateExternalSystem(externalSystem));
    }

    /**
     * 删除外部系统配置
     */
    @ApiOperation("删除外部系统配置")
    @PreAuthorize("@ss.hasPermi('system:external:remove')")
    @Log(title = "外部系统配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{systemIds}")
    public AjaxResult remove(@ApiParam("系统ID数组") @PathVariable Long[] systemIds)
    {
        return toAjax(externalSystemService.deleteExternalSystemBySystemIds(systemIds));
    }

    /**
     * 重新生成API密钥
     */
    @ApiOperation("重新生成API密钥")
    @PreAuthorize("@ss.hasPermi('system:external:edit')")
    @Log(title = "外部系统配置", businessType = BusinessType.UPDATE)
    @PostMapping("/regenerateApiKey/{systemId}")
    public AjaxResult regenerateApiKey(@ApiParam("系统ID") @PathVariable Long systemId)
    {
        int result = externalSystemService.regenerateApiKey(systemId);
        if (result > 0)
        {
            // 重新查询返回新的密钥信息
            ExternalSystem externalSystem = externalSystemService.selectExternalSystemBySystemId(systemId);
            AjaxResult ajax = AjaxResult.success("API密钥重新生成成功");
            ajax.put("data", externalSystem);
            return ajax;
        }
        else
        {
            return error("API密钥重新生成失败");
        }
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
    @PreAuthorize("@ss.hasPermi('system:external:edit')")
    @Log(title = "外部系统配置", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody ExternalSystem externalSystem)
    {
        return toAjax(externalSystemService.changeStatus(externalSystem.getSystemId(), externalSystem.getStatus()));
    }

    /**
     * 校验系统编码
     */
    @ApiOperation("校验系统编码")
    @PostMapping("/checkSystemCodeUnique")
    public boolean checkSystemCodeUnique(@RequestBody ExternalSystem externalSystem)
    {
        return externalSystemService.checkSystemCodeUnique(externalSystem);
    }

    /**
     * 校验系统名称
     */
    @ApiOperation("校验系统名称")
    @PostMapping("/checkSystemNameUnique")
    public boolean checkSystemNameUnique(@RequestBody ExternalSystem externalSystem)
    {
        return externalSystemService.checkSystemNameUnique(externalSystem);
    }

    /**
     * 获取API调用示例
     */
    @ApiOperation("获取API调用示例")
    @PreAuthorize("@ss.hasPermi('system:external:query')")
    @GetMapping("/apiExample/{systemId}")
    public AjaxResult getApiExample(@ApiParam("系统ID") @PathVariable Long systemId)
    {
        ExternalSystem externalSystem = externalSystemService.selectExternalSystemBySystemId(systemId);
        if (externalSystem == null)
        {
            return error("系统不存在");
        }

        // 构建API调用示例
        StringBuilder example = new StringBuilder();
        example.append("# API调用示例\n\n");
        example.append("## 请求头设置\n");
        example.append("```\n");
        example.append("X-API-Key: ").append(externalSystem.getApiKey()).append("\n");
        example.append("X-Timestamp: ").append(System.currentTimeMillis() / 1000).append("\n");
        example.append("X-Signature: [根据签名算法生成]\n");
        example.append("Content-Type: application/json\n");
        example.append("```\n\n");
        
        example.append("## 签名算法\n");
        example.append("```\n");
        example.append("签名字符串 = HTTP方法 + URI + 查询参数 + 时间戳\n");
        example.append("签名 = HMAC-SHA256(签名字符串, API密钥密文)\n");
        example.append("```\n\n");
        
        example.append("## 请求示例\n");
        example.append("```bash\n");
        example.append("curl -X GET \\\n");
        example.append("  'http://localhost:8080/external/api/inspiration/categories' \\\n");
        example.append("  -H 'X-API-Key: ").append(externalSystem.getApiKey()).append("' \\\n");
        example.append("  -H 'X-Timestamp: ").append(System.currentTimeMillis() / 1000).append("' \\\n");
        example.append("  -H 'X-Signature: [计算得出的签名]' \\\n");
        example.append("  -H 'Content-Type: application/json'\n");
        example.append("```\n");

        return success(example.toString());
    }

    /**
     * 测试接口 - 验证控制器是否正常工作
     */
    @GetMapping("/test")
    public AjaxResult test()
    {
        return AjaxResult.success("外部系统控制器工作正常");
    }
}
