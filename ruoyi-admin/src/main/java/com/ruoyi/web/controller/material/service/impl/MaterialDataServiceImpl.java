package com.ruoyi.web.controller.material.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.web.controller.external.service.InspirationSquareService;
import com.ruoyi.web.controller.external.domain.InspirationMaterial;
import com.ruoyi.web.controller.material.service.MaterialDataService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * 素材数据服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class MaterialDataServiceImpl implements MaterialDataService
{
    private static final Logger log = LoggerFactory.getLogger(MaterialDataServiceImpl.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private InspirationSquareService inspirationSquareService;

    /** 本地素材存储路径 */
    @Value("${ruoyi.profile:/home/<USER>/uploadPath}")
    private String uploadPath;

    @Override
    public List<Map<String, Object>> getPersonalMaterials(Long userId)
    {
        List<Map<String, Object>> materials = new ArrayList<>();
        
        try
        {
            // 这里应该从数据库查询用户的个人创作素材
            // 目前提供模拟数据，实际项目中需要创建相应的数据表和查询逻辑
            
            // 模拟图片素材数据
            Map<String, Object> imageMaterial1 = new HashMap<>();
            imageMaterial1.put("type", 1);
            imageMaterial1.put("title", "我的创作图片1");
            imageMaterial1.put("photo", buildFullUrl("/profile/materials/thumbnails/image1_thumb.jpg"));
            imageMaterial1.put("photourl", buildFullUrl("/profile/materials/images/image1.jpg"));
            imageMaterial1.put("tag", "人物");
            imageMaterial1.put("guiId", "personal_img_" + System.currentTimeMillis() + "_1");
            materials.add(imageMaterial1);

            Map<String, Object> imageMaterial2 = new HashMap<>();
            imageMaterial2.put("type", 1);
            imageMaterial2.put("title", "我的创作图片2");
            imageMaterial2.put("photo", buildFullUrl("/profile/materials/thumbnails/image2_thumb.jpg"));
            imageMaterial2.put("photourl", buildFullUrl("/profile/materials/images/image2.jpg"));
            imageMaterial2.put("tag", "风景");
            imageMaterial2.put("guiId", "personal_img_" + System.currentTimeMillis() + "_2");
            materials.add(imageMaterial2);

            // 模拟3D模型素材数据
            Map<String, Object> modelMaterial1 = new HashMap<>();
            modelMaterial1.put("type", 2);
            modelMaterial1.put("title", "我的3D模型1");
            modelMaterial1.put("photo", buildFullUrl("/profile/materials/thumbnails/model1_thumb.jpg"));
            modelMaterial1.put("photourl", buildFullUrl("/profile/materials/models/model1.glb"));
            modelMaterial1.put("tag", "建筑");
            modelMaterial1.put("guiId", "personal_model_" + System.currentTimeMillis() + "_1");
            materials.add(modelMaterial1);

            log.info("获取用户 {} 的个人创作素材，共 {} 个", userId, materials.size());
        }
        catch (Exception e)
        {
            log.error("获取用户个人创作素材失败，用户ID: {}", userId, e);
        }
        
        return materials;
    }

    @Override
    public List<Map<String, Object>> getInspirationMaterials()
    {
        List<Map<String, Object>> materials = new ArrayList<>();
        
        try
        {
            // 尝试从灵感广场系统获取数据
            List<InspirationMaterial> inspirationMaterials = inspirationSquareService.getMaterials(null, null, null);
            
            if (inspirationMaterials != null && !inspirationMaterials.isEmpty())
            {
                // 转换灵感广场数据格式
                for (InspirationMaterial material : inspirationMaterials)
                {
                    Map<String, Object> item = new HashMap<>();
                    item.put("title", material.getTitle());
                    item.put("photourl", material.getThumbnailUrl());
                    item.put("detailurl", material.getMaterialUrl());
                    item.put("tag", StringUtils.isNotEmpty(material.getCategoryName()) ? material.getCategoryName() : "其他");
                    item.put("guiId", "inspiration_" + material.getMaterialId());
                    materials.add(item);
                }
                
                log.info("从灵感广场系统获取素材 {} 个", materials.size());
            }
            else
            {
                // 如果灵感广场系统不可用，提供模拟数据
                materials = getDefaultInspirationMaterials();
                log.info("灵感广场系统不可用，使用模拟数据 {} 个", materials.size());
            }
        }
        catch (Exception e)
        {
            log.error("获取灵感广场素材失败，使用模拟数据", e);
            materials = getDefaultInspirationMaterials();
        }
        
        return materials;
    }

    /**
     * 获取默认的灵感广场素材数据（模拟数据）
     */
    private List<Map<String, Object>> getDefaultInspirationMaterials()
    {
        List<Map<String, Object>> materials = new ArrayList<>();
        
        // 人物标签素材
        Map<String, Object> material1 = new HashMap<>();
        material1.put("title", "经典人物肖像");
        material1.put("photourl", "http://example.com/thumbnails/portrait1.jpg");
        material1.put("detailurl", "http://example.com/images/portrait1_detail.jpg");
        material1.put("tag", "人物");
        material1.put("guiId", "inspiration_default_1");
        materials.add(material1);

        Map<String, Object> material2 = new HashMap<>();
        material2.put("title", "现代人物设计");
        material2.put("photourl", "http://example.com/thumbnails/portrait2.jpg");
        material2.put("detailurl", "http://example.com/images/portrait2_detail.jpg");
        material2.put("tag", "人物");
        material2.put("guiId", "inspiration_default_2");
        materials.add(material2);

        // 风景标签素材
        Map<String, Object> material3 = new HashMap<>();
        material3.put("title", "山水风光");
        material3.put("photourl", "http://example.com/thumbnails/landscape1.jpg");
        material3.put("detailurl", "http://example.com/images/landscape1_detail.jpg");
        material3.put("tag", "风景");
        material3.put("guiId", "inspiration_default_3");
        materials.add(material3);

        Map<String, Object> material4 = new HashMap<>();
        material4.put("title", "城市夜景");
        material4.put("photourl", "http://example.com/thumbnails/cityscape1.jpg");
        material4.put("detailurl", "http://example.com/images/cityscape1_detail.jpg");
        material4.put("tag", "风景");
        material4.put("guiId", "inspiration_default_4");
        materials.add(material4);

        // 建筑标签素材
        Map<String, Object> material5 = new HashMap<>();
        material5.put("title", "古典建筑");
        material5.put("photourl", "http://example.com/thumbnails/architecture1.jpg");
        material5.put("detailurl", "http://example.com/images/architecture1_detail.jpg");
        material5.put("tag", "建筑");
        material5.put("guiId", "inspiration_default_5");
        materials.add(material5);

        Map<String, Object> material6 = new HashMap<>();
        material6.put("title", "现代建筑");
        material6.put("photourl", "http://example.com/thumbnails/architecture2.jpg");
        material6.put("detailurl", "http://example.com/images/architecture2_detail.jpg");
        material6.put("tag", "建筑");
        material6.put("guiId", "inspiration_default_6");
        materials.add(material6);

        return materials;
    }

    /**
     * 构建完整的URL地址
     */
    private String buildFullUrl(String relativePath)
    {
        if (StringUtils.isEmpty(relativePath))
        {
            return "";
        }
        
        if (relativePath.startsWith("http"))
        {
            return relativePath;
        }
        
        return serverConfig.getUrl() + relativePath;
    }
}
