package com.ruoyi.web.controller.external.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.ByteArrayResource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import com.ruoyi.common.utils.file.FileTypeUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.web.controller.external.domain.InspirationMaterial;
import com.ruoyi.web.controller.external.domain.InspirationCategory;
import com.ruoyi.web.controller.external.service.InspirationSquareService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.model.LoginUser;

/**
 * 灵感广场数据服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class InspirationSquareServiceImpl implements InspirationSquareService
{
    private static final Logger log = LoggerFactory.getLogger(InspirationSquareServiceImpl.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RestTemplate restTemplate;

    /** 灵感广场系统基础URL */
    @Value("${inspiration.square.base-url:http://localhost:9001}")
    private String inspirationSquareBaseUrl;

    /** 灵感广场系统API密钥 */
    @Value("${inspiration.square.api-key:}")
    private String inspirationSquareApiKey;

    /** Immich服务器地址 */
    @Value("${inspiration.square.immich-server:*************:2283}")
    private String immichServerAddress;

    /** 缓存键前缀 */
    private static final String CACHE_PREFIX = "inspiration_square:";

    /** 分类缓存时间（分钟） */
    private static final int CATEGORY_CACHE_MINUTES = 30;

    /** 素材缓存时间（分钟） */
    private static final int MATERIAL_CACHE_MINUTES = 10;

    @Override
    public List<InspirationCategory> getCategories()
    {
        String cacheKey = CACHE_PREFIX + "categories";
        
        // 先从缓存获取
        List<InspirationCategory> cachedCategories = redisCache.getCacheObject(cacheKey);
        if (cachedCategories != null)
        {
            return cachedCategories;
        }

        try
        {
            // 调用灵感广场API获取分类数据
            String url = inspirationSquareBaseUrl + "/api/categories";
            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONArray dataArray = jsonResponse.getJSONArray("data");
                    List<InspirationCategory> categories = dataArray.toList(InspirationCategory.class);
                    
                    // 缓存结果
                    redisCache.setCacheObject(cacheKey, categories, CATEGORY_CACHE_MINUTES, java.util.concurrent.TimeUnit.MINUTES);
                    
                    return categories;
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取灵感广场分类数据失败", e);
        }

        // 返回默认分类数据
        return getDefaultCategories();
    }

    @Override
    public List<InspirationMaterial> getMaterials(Long categoryId, String keyword, String materialType)
    {
        try
        {
            // 构建请求参数
            StringBuilder params = new StringBuilder();
            if (categoryId != null)
            {
                params.append("categoryId=").append(categoryId).append("&");
            }
            if (StringUtils.isNotEmpty(keyword))
            {
                params.append("keyword=").append(keyword).append("&");
            }
            if (StringUtils.isNotEmpty(materialType))
            {
                params.append("materialType=").append(materialType).append("&");
            }

            String url = inspirationSquareBaseUrl + "/api/materials";
            if (params.length() > 0)
            {
                url += "?" + params.toString().replaceAll("&$", "");
            }

            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONArray dataArray = jsonResponse.getJSONArray("data");
                    return dataArray.toList(InspirationMaterial.class);
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取灵感广场素材数据失败", e);
        }

        // 返回默认素材数据
        return getDefaultMaterials();
    }

    @Override
    public InspirationMaterial getMaterialById(Long materialId)
    {
        String cacheKey = CACHE_PREFIX + "material:" + materialId;
        
        // 先从缓存获取
        InspirationMaterial cachedMaterial = redisCache.getCacheObject(cacheKey);
        if (cachedMaterial != null)
        {
            return cachedMaterial;
        }

        try
        {
            String url = inspirationSquareBaseUrl + "/api/materials/" + materialId;
            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONObject dataObject = jsonResponse.getJSONObject("data");
                    InspirationMaterial material = dataObject.toJavaObject(InspirationMaterial.class);
                    
                    // 缓存结果
                    redisCache.setCacheObject(cacheKey, material, MATERIAL_CACHE_MINUTES, java.util.concurrent.TimeUnit.MINUTES);
                    
                    return material;
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取灵感广场素材详情失败，素材ID: {}", materialId, e);
        }

        return null;
    }

    @Override
    public List<InspirationMaterial> searchMaterials(InspirationMaterial searchCriteria)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/api/materials/search";
            String requestBody = JSON.toJSONString(searchCriteria);
            String response = callInspirationSquareApi(url, "POST", requestBody);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONArray dataArray = jsonResponse.getJSONArray("data");
                    return dataArray.toList(InspirationMaterial.class);
                }
            }
        }
        catch (Exception e)
        {
            log.error("搜索灵感广场素材失败", e);
        }

        return new ArrayList<>();
    }

    @Override
    public List<InspirationMaterial> getHotMaterials(Integer limit)
    {
        String cacheKey = CACHE_PREFIX + "hot_materials:" + limit;
        
        // 先从缓存获取
        List<InspirationMaterial> cachedMaterials = redisCache.getCacheObject(cacheKey);
        if (cachedMaterials != null)
        {
            return cachedMaterials;
        }

        try
        {
            String url = inspirationSquareBaseUrl + "/api/materials/hot?limit=" + limit;
            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONArray dataArray = jsonResponse.getJSONArray("data");
                    List<InspirationMaterial> materials = dataArray.toList(InspirationMaterial.class);
                    
                    // 缓存结果
                    redisCache.setCacheObject(cacheKey, materials, MATERIAL_CACHE_MINUTES, java.util.concurrent.TimeUnit.MINUTES);
                    
                    return materials;
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取热门素材失败", e);
        }

        return new ArrayList<>();
    }

    @Override
    public List<InspirationMaterial> getLatestMaterials(Integer limit)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/api/materials/latest?limit=" + limit;
            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    JSONArray dataArray = jsonResponse.getJSONArray("data");
                    return dataArray.toList(InspirationMaterial.class);
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取最新素材失败", e);
        }

        return new ArrayList<>();
    }

    @Override
    public boolean recordMaterialVisit(Long materialId, String systemCode)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/api/materials/" + materialId + "/visit";
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("systemCode", systemCode);
            requestData.put("visitTime", new Date());
            
            String requestBody = JSON.toJSONString(requestData);
            String response = callInspirationSquareApi(url, "POST", requestBody);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                return jsonResponse.getInteger("code") == 200;
            }
        }
        catch (Exception e)
        {
            log.error("记录素材访问失败，素材ID: {}, 系统编码: {}", materialId, systemCode, e);
        }

        return false;
    }

    @Override
    public Object getStatistics(String systemCode)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/api/statistics?systemCode=" + systemCode;
            String response = callInspirationSquareApi(url, "GET", null);
            
            if (StringUtils.isNotEmpty(response))
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("code") == 200)
                {
                    return jsonResponse.get("data");
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取统计信息失败，系统编码: {}", systemCode, e);
        }

        // 返回默认统计信息
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("totalMaterials", 0);
        defaultStats.put("totalCategories", 0);
        defaultStats.put("totalVisits", 0);
        defaultStats.put("lastUpdateTime", new Date());
        
        return defaultStats;
    }

    /**
     * 调用灵感广场API（使用RestTemplate）
     */
    private String callInspirationSquareApi(String url, String method, String requestBody)
    {
        try
        {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");

            if ("GET".equals(method))
            {
                HttpEntity<String> entity = new HttpEntity<>(headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
                return response.getStatusCode().is2xxSuccessful() ? response.getBody() : null;
            }
            else if ("POST".equals(method))
            {
                HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
                return response.getStatusCode().is2xxSuccessful() ? response.getBody() : null;
            }
        }
        catch (Exception e)
        {
            log.error("调用灵感广场API失败，URL: {}, 方法: {}", url, method, e);
        }

        return null;
    }

    /**
     * 获取默认分类数据（当外部系统不可用时）
     */
    private List<InspirationCategory> getDefaultCategories()
    {
        List<InspirationCategory> categories = new ArrayList<>();
        
        InspirationCategory category1 = new InspirationCategory();
        category1.setCategoryId(1L);
        category1.setCategoryName("图片素材");
        category1.setCategoryCode("IMAGE");
        category1.setDescription("各类图片素材");
        category1.setMaterialCount(100L);
        categories.add(category1);
        
        InspirationCategory category2 = new InspirationCategory();
        category2.setCategoryId(2L);
        category2.setCategoryName("视频素材");
        category2.setCategoryCode("VIDEO");
        category2.setDescription("各类视频素材");
        category2.setMaterialCount(50L);
        categories.add(category2);
        
        return categories;
    }

    /**
     * 获取默认素材数据（当外部系统不可用时）
     */
    private List<InspirationMaterial> getDefaultMaterials()
    {
        List<InspirationMaterial> materials = new ArrayList<>();
        
        InspirationMaterial material = new InspirationMaterial();
        material.setMaterialId(1L);
        material.setTitle("示例素材");
        material.setDescription("这是一个示例素材");
        material.setMaterialType("IMAGE");
        material.setCategoryId(1L);
        material.setCategoryName("图片素材");
        material.setIsFree(true);
        material.setViewCount(100L);
        material.setCreateTime(new Date());
        materials.add(material);
        
        return materials;
    }

    @Override
    public List<Map<String, Object>> getPersonalMaterials(String token)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/external/api/inspiration/GetAllPersonDetail";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.set("Content-Type", "application/json");

            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送GET请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && StringUtils.isNotEmpty(response.getBody()))
            {
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                if (jsonResponse.getInteger("code") == 200 && "success".equals(jsonResponse.getString("msg")))
                {
                    return jsonResponse.getObject("data", List.class);
                }
                else
                {
                    log.warn("外部系统返回错误: {}", jsonResponse.getString("msg"));
                }
            }
        }
        catch (Exception e)
        {
            log.error("调用外部系统获取个人创作素材失败", e);
        }

        // 返回模拟数据
        return getDefaultPersonalMaterials();
    }

    @Override
    public List<Map<String, Object>> getInspirationMaterials(String token, String userId,String tag)
    {
        try
        {
            String url = inspirationSquareBaseUrl + "/external/api/inspiration/GetAllContentDetail";
            if (StringUtils.isNotEmpty(userId)) {
                url += "?userId=" + userId;
            }
            if (StringUtils.isNotEmpty(tag)) {
                url += "?tag=" + tag;
            }

            log.info("准备调用Immich接口: {}", url);

            // 使用配置的API Key而不是传入的token
            String apiToken = StringUtils.isNotEmpty(inspirationSquareApiKey) ? inspirationSquareApiKey : token;
            log.info("使用Token: {}", apiToken);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiToken);
            headers.set("Content-Type", "application/json");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            log.info("发送请求头: {}", headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            log.info("响应状态: {}", response.getStatusCode());
            log.info("响应头: {}", response.getHeaders());
            log.info("响应体: {}", response.getBody());

            if (response.getStatusCode().is2xxSuccessful() && StringUtils.isNotEmpty(response.getBody()))
            {
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                if (jsonResponse.getInteger("code") == 200 && "success".equals(jsonResponse.getString("msg")))
                {
                    return jsonResponse.getObject("data", List.class);
                }
                else
                {
                    log.warn("外部系统返回错误: {}", jsonResponse.getString("msg"));
                }
            }
        }
        catch (Exception e)
        {
            log.error("调用外部系统获取灵感广场素材失败", e);
        }

        // 返回模拟数据
        return getDefaultInspirationMaterials();
    }

    /**
     * 获取默认个人创作素材数据
     */
    private List<Map<String, Object>> getDefaultPersonalMaterials()
    {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 图片类型素材
        List<Map<String, Object>> imageList = new ArrayList<>();
        Map<String, Object> imageMaterial1 = new HashMap<>();
        imageMaterial1.put("title", "我的创作图片1");
        imageMaterial1.put("photo", "http://localhost:8080/profile/materials/thumbnails/image1_thumb.jpg");
        imageMaterial1.put("photourl", "http://localhost:8080/profile/materials/images/image1.jpg");
        imageMaterial1.put("tag", "人物");
        imageMaterial1.put("guiId", "personal_img_" + System.currentTimeMillis() + "_1");
        imageList.add(imageMaterial1);

        Map<String, Object> imageMaterial2 = new HashMap<>();
        imageMaterial2.put("title", "我的创作图片2");
        imageMaterial2.put("photo", "http://localhost:8080/profile/materials/thumbnails/image2_thumb.jpg");
        imageMaterial2.put("photourl", "http://localhost:8080/profile/materials/images/image2.jpg");
        imageMaterial2.put("tag", "风景");
        imageMaterial2.put("guiId", "personal_img_" + System.currentTimeMillis() + "_2");
        imageList.add(imageMaterial2);

        // 3D模型类型素材
        List<Map<String, Object>> modelList = new ArrayList<>();
        Map<String, Object> modelMaterial1 = new HashMap<>();
        modelMaterial1.put("title", "我的3D模型1");
        modelMaterial1.put("photo", "http://localhost:8080/profile/materials/thumbnails/model1_thumb.jpg");
        modelMaterial1.put("photourl", "http://localhost:8080/profile/materials/models/model1.glb");
        modelMaterial1.put("tag", "建筑");
        modelMaterial1.put("guiId", "personal_model_" + System.currentTimeMillis() + "_1");
        modelList.add(modelMaterial1);

        // 按类型分组
        if (!imageList.isEmpty())
        {
            Map<String, Object> imageGroup = new HashMap<>();
            imageGroup.put("type", 1);
            imageGroup.put("List", imageList);
            dataList.add(imageGroup);
        }

        if (!modelList.isEmpty())
        {
            Map<String, Object> modelGroup = new HashMap<>();
            modelGroup.put("type", 2);
            modelGroup.put("List", modelList);
            dataList.add(modelGroup);
        }

        return dataList;
    }

    /**
     * 获取默认灵感广场素材数据
     */
    private List<Map<String, Object>> getDefaultInspirationMaterials()
    {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 人物标签
        List<Map<String, Object>> personList = new ArrayList<>();
        Map<String, Object> person1 = new HashMap<>();
        person1.put("title", "经典人物肖像");
        person1.put("photourl", "http://example.com/thumbnails/portrait1.jpg");
        person1.put("detailurl", "http://example.com/images/portrait1_detail.jpg");
        person1.put("guiId", "inspiration_default_1");
        personList.add(person1);

        Map<String, Object> person2 = new HashMap<>();
        person2.put("title", "现代人物设计");
        person2.put("photourl", "http://example.com/thumbnails/portrait2.jpg");
        person2.put("detailurl", "http://example.com/images/portrait2_detail.jpg");
        person2.put("guiId", "inspiration_default_2");
        personList.add(person2);

        Map<String, Object> personGroup = new HashMap<>();
        personGroup.put("tag", "人物");
        personGroup.put("tagnum", personList.size());
        personGroup.put("List", personList);
        dataList.add(personGroup);

        // 风景标签
        List<Map<String, Object>> landscapeList = new ArrayList<>();
        Map<String, Object> landscape1 = new HashMap<>();
        landscape1.put("title", "山水风光");
        landscape1.put("photourl", "http://example.com/thumbnails/landscape1.jpg");
        landscape1.put("detailurl", "http://example.com/images/landscape1_detail.jpg");
        landscape1.put("guiId", "inspiration_default_3");
        landscapeList.add(landscape1);

        Map<String, Object> landscape2 = new HashMap<>();
        landscape2.put("title", "城市夜景");
        landscape2.put("photourl", "http://example.com/thumbnails/cityscape1.jpg");
        landscape2.put("detailurl", "http://example.com/images/cityscape1_detail.jpg");
        landscape2.put("guiId", "inspiration_default_4");
        landscapeList.add(landscape2);

        Map<String, Object> landscapeGroup = new HashMap<>();
        landscapeGroup.put("tag", "风景");
        landscapeGroup.put("tagnum", landscapeList.size());
        landscapeGroup.put("List", landscapeList);
        dataList.add(landscapeGroup);

        return dataList;
    }

    @Override
    public Map<String, Object> uploadMaterial(MultipartFile file, String title,
                                              String description, String tag, String status, HttpServletRequest request)
    {
        try
        {
            // 验证文件
            if (file == null || file.isEmpty())
            {
                throw new RuntimeException("上传文件不能为空");
            }

            // 验证文件类型（只允许图片）
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/"))
            {
                throw new RuntimeException("只支持图片文件上传");
            }

            // 验证文件大小（限制50MB，主要针对大图片）
            if (file.getSize() > 50 * 1024 * 1024)
            {
                throw new RuntimeException("文件大小不能超过50MB");
            }

            // 构建上传URL
            String uploadUrl = inspirationSquareBaseUrl + "/external/api/inspiration/upload";

            // 获取用户Token（优先从Token头获取，备选从Authorization头获取）
            String token = request.getHeader("Token");
            if (StringUtils.isEmpty(token))
            {
                String authHeader = request.getHeader("Authorization");
                if (StringUtils.isNotEmpty(authHeader))
                {
                    // 如果包含Bearer前缀，去掉前缀
                    if (authHeader.startsWith("Bearer "))
                    {
                        token = authHeader.substring(7);
                    }
                    else
                    {
                        // 直接使用Authorization头的值作为token
                        token = authHeader;
                    }
                }
            }

            if (StringUtils.isEmpty(token))
            {
                throw new RuntimeException("Token验证失败");
            }

            // 构建multipart请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Authorization", "Bearer " + token);

            // 构建multipart body
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件
            ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file", fileResource);

            // 添加其他参数
            if (StringUtils.isNotEmpty(title)) {
                body.add("title", title);
            }
            if (StringUtils.isNotEmpty(description)) {
                body.add("description", description);
            }
            if (StringUtils.isNotEmpty(tag)) {
                body.add("tag", tag);
            }
            if (StringUtils.isNotEmpty(status)) {
                body.add("status", status);
            }

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(uploadUrl, HttpMethod.POST, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && StringUtils.isNotEmpty(response.getBody()))
            {
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                if (jsonResponse.getInteger("code") == 200)
                {
                    // 返回成功结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("materialId", jsonResponse.getString("materialId"));
                    result.put("fileName", file.getOriginalFilename());
                    result.put("fileSize", file.getSize());
                    result.put("uploadTime", new Date());
                    result.put("materialUrl", jsonResponse.getString("materialUrl"));
                    result.put("thumbnailUrl", jsonResponse.getString("thumbnailUrl"));
                    return result;
                }
                else
                {
                    throw new RuntimeException("上传失败: " + jsonResponse.getString("msg"));
                }
            }
            else
            {
                throw new RuntimeException("上传请求失败，状态码: " + response.getStatusCode());
            }
        }
        catch (Exception e)
        {
            log.error("上传文件到灵感广场失败", e);
            throw new RuntimeException("上传失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> uploadModel(MultipartFile file, MultipartFile modelFile, String title,
                                         String type, String tag, String status, HttpServletRequest request)
    {
        try
        {
            // 验证预览图片文件
            if (file == null || file.isEmpty())
            {
                throw new RuntimeException("模型预览图片不能为空");
            }

            // 验证模型文件
            if (modelFile == null || modelFile.isEmpty())
            {
                throw new RuntimeException("模型文件不能为空");
            }

            // 验证预览图片类型（只允许图片）
            String imageContentType = file.getContentType();
            if (imageContentType == null || !imageContentType.startsWith("image/"))
            {
                throw new RuntimeException("预览图片只支持图片文件格式");
            }

            // 验证模型文件类型
            String modelFileName = modelFile.getOriginalFilename();
            if (!isValidModelFile(modelFileName))
            {
                throw new RuntimeException("模型文件格式不支持，支持格式：.glb, .gltf, .obj, .fbx, .3ds, .dae, .ply");
            }

            // 验证文件大小
            if (file.getSize() > 10 * 1024 * 1024)
            {
                throw new RuntimeException("预览图片大小不能超过10MB");
            }

            if (modelFile.getSize() > 50 * 1024 * 1024)
            {
                throw new RuntimeException("模型文件大小不能超过50MB");
            }

            // 构建上传URL
            String uploadUrl = inspirationSquareBaseUrl + "/external/api/inspiration/uploadModel";

            // 获取用户Token（优先从Token头获取，备选从Authorization头获取）
            String token = request.getHeader("Token");
            if (StringUtils.isEmpty(token))
            {
                String authHeader = request.getHeader("Authorization");
                if (StringUtils.isNotEmpty(authHeader))
                {
                    // 如果包含Bearer前缀，去掉前缀
                    if (authHeader.startsWith("Bearer "))
                    {
                        token = authHeader.substring(7);
                    }
                    else
                    {
                        // 直接使用Authorization头的值作为token
                        token = authHeader;
                    }
                }
            }

            if (StringUtils.isEmpty(token))
            {
                throw new RuntimeException("Token验证失败");
            }

            // 构建multipart请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Authorization", "Bearer " + token);

            // 构建multipart body
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加预览图片文件
            ByteArrayResource imageResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("file", imageResource);

            // 添加模型文件
            ByteArrayResource modelResource = new ByteArrayResource(modelFile.getBytes()) {
                @Override
                public String getFilename() {
                    return modelFile.getOriginalFilename();
                }
            };
            body.add("modelFile", modelResource);

            // 添加其他参数
            if (StringUtils.isNotEmpty(title)) {
                body.add("title", title);
            }
            if (StringUtils.isNotEmpty(type)) {
                body.add("description", type);
            }
            if (StringUtils.isNotEmpty(tag)) {
                body.add("tag", tag);
            }
            if (StringUtils.isNotEmpty(status)) {
                body.add("status", status);
            }
            if (StringUtils.isNotEmpty(type)) {
                body.add("type", type);
            }
            if (StringUtils.isNotEmpty(tag)) {
                body.add("tag", tag);
            }

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(uploadUrl, HttpMethod.POST, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && StringUtils.isNotEmpty(response.getBody()))
            {
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                if (jsonResponse.getInteger("code") == 200)
                {
                    // 返回成功结果
                    Map<String, Object> result = new HashMap<>();
                    result.put("materialId", jsonResponse.getString("materialId"));
                    result.put("fileName", file.getOriginalFilename());
                    result.put("modelFileName", modelFile.getOriginalFilename());
                    result.put("fileSize", file.getSize());
                    result.put("modelFileSize", modelFile.getSize());
                    result.put("uploadTime", new Date());
                    result.put("materialUrl", jsonResponse.getString("materialUrl"));
                    result.put("thumbnailUrl", jsonResponse.getString("thumbnailUrl"));
                    result.put("modelUrl", jsonResponse.getString("modelUrl"));
                    result.put("type", "model");
                    return result;
                }
                else
                {
                    throw new RuntimeException("上传失败: " + jsonResponse.getString("msg"));
                }
            }
            else
            {
                throw new RuntimeException("上传请求失败，状态码: " + response.getStatusCode());
            }
        }
        catch (Exception e)
        {
            log.error("上传模型到灵感广场失败", e);
            throw new RuntimeException("上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证模型文件格式
     */
    private boolean isValidModelFile(String fileName)
    {
        if (StringUtils.isEmpty(fileName))
        {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".glb") ||
               lowerFileName.endsWith(".gltf") ||
               lowerFileName.endsWith(".obj") ||
               lowerFileName.endsWith(".fbx") ||
               lowerFileName.endsWith(".3ds") ||
               lowerFileName.endsWith(".dae") ||
               lowerFileName.endsWith(".ply");
    }

    @Override
    public void downloadMaterial(String materialId, String type, HttpServletResponse response)
    {
        try
        {
            // 获取当前用户的token
            String userToken = getCurrentUserToken();

            // 获取素材详情
            Map<String, Object> materialInfo = getMaterialInfo(materialId);
            if (materialInfo == null)
            {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":404,\"msg\":\"素材不存在\"}");
                return;
            }

            // 根据类型获取对应的文件URL
            String fileUrl = getFileUrlByType(materialInfo, type);
            if (StringUtils.isEmpty(fileUrl))
            {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":400,\"msg\":\"不支持的文件类型或文件不存在\"}");
                return;
            }

            // 下载并传输文件，传递用户token
            downloadAndTransferFile(fileUrl, materialInfo, type, response, userToken);
        }
        catch (Exception e)
        {
            log.error("下载素材文件失败，素材ID: {}, 类型: {}", materialId, type, e);
            try
            {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"下载失败: " + e.getMessage() + "\"}");
            }
            catch (Exception ex)
            {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 获取素材信息
     */
    private Map<String, Object> getMaterialInfo(String materialId)
    {
        try
        {
            // 使用现有的getInspirationMaterials方法获取素材列表
            List<Map<String, Object>> materials = getInspirationMaterials("system_token", "admin", null);

            // 遍历所有分组查找指定ID的素材
            for (Map<String, Object> group : materials)
            {
                // 遍历每个分组中的标签
                for (Map.Entry<String, Object> entry : group.entrySet())
                {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    // 跳过非素材列表的字段
                    if ("categories".equals(key) || "tag".equals(key) || "tagnum".equals(key))
                    {
                        continue;
                    }

                    // 检查是否为素材列表
                    if (value instanceof List)
                    {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> materialList = (List<Map<String, Object>>) value;

                        for (Map<String, Object> material : materialList)
                        {
                            // 检查guidId字段（注意API返回的是guidId，不是guiId）
                            String guidId = (String) material.get("guidId");
                            if (materialId.equals(guidId))
                            {
                                // 添加标签信息
                                material.put("tag", key);
                                return material;
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error("获取素材信息失败，素材ID: {}", materialId, e);
        }
        return null;
    }

    /**
     * 根据类型获取文件URL
     */
    private String getFileUrlByType(Map<String, Object> materialInfo, String type)
    {
        switch (type.toLowerCase())
        {
            case "original":
                // API返回的字段是filepath，对应原始文件
                return (String) materialInfo.get("filepath");
            case "thumbnail":
                // API返回的字段是thumbnail，对应缩略图
                return (String) materialInfo.get("thumbnail");
            case "model":
                // 检查是否为模型类型素材
                String materialType = (String) materialInfo.get("type");
                if ("model".equals(materialType))
                {
                    // 对于模型文件，使用filepath作为模型文件URL
                    return (String) materialInfo.get("filepath");
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * 下载并传输文件
     */
    private void downloadAndTransferFile(String fileUrl, Map<String, Object> materialInfo, String type, HttpServletResponse response, String userToken) throws Exception
    {
        // 修复URL中的localhost问题
        String actualFileUrl = fixLocalhostUrl(fileUrl);
        log.info("原始URL: {}, 修复后URL: {}", fileUrl, actualFileUrl);

        URL url = new URL(actualFileUrl);
        URLConnection connection = url.openConnection();
        connection.setConnectTimeout(10000); // 10秒连接超时
        connection.setReadTimeout(30000);    // 30秒读取超时

        // 添加认证头 - 使用用户token而不是系统token
        if (StringUtils.isNotEmpty(userToken)) {
            connection.setRequestProperty("Authorization", "Bearer " + userToken);
            log.info("添加认证头: Authorization: Bearer {}", userToken.substring(0, Math.min(userToken.length(), 10)) + "...");
        } else {
            // 如果没有用户token，尝试使用系统token作为备用
            connection.setRequestProperty("Authorization", "Bearer system_token");
            log.info("使用系统token作为备用认证");
        }

        // 获取文件信息
        String fileName = getFileNameFromUrl(fileUrl, materialInfo, type);
        String contentType = connection.getContentType();
        long contentLength = connection.getContentLengthLong();

        // 设置响应头
        if (StringUtils.isEmpty(contentType))
        {
            contentType = getContentTypeByFileName(fileName);
        }
        response.setContentType(contentType);

        if (contentLength > 0)
        {
            response.setContentLengthLong(contentLength);
        }

        // 设置下载文件名
        FileUtils.setAttachmentResponseHeader(response, fileName);

        // 传输文件流
        try (InputStream inputStream = connection.getInputStream();
             OutputStream outputStream = response.getOutputStream())
        {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1)
            {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    /**
     * 从URL和素材信息中获取文件名
     */
    private String getFileNameFromUrl(String fileUrl, Map<String, Object> materialInfo, String type)
    {
        String title = (String) materialInfo.get("title");
        if (StringUtils.isEmpty(title))
        {
            // API返回的ID字段是guidId
            title = "material_" + materialInfo.get("guidId");
        }

        // 清理文件名中的特殊字符
        title = title.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_");

        // 从URL中提取文件扩展名
        String extension = "";
        try
        {
            String urlPath = new URL(fileUrl).getPath();
            int lastDotIndex = urlPath.lastIndexOf('.');
            if (lastDotIndex > 0)
            {
                extension = urlPath.substring(lastDotIndex);
            }
        }
        catch (Exception e)
        {
            log.warn("无法从URL提取扩展名: {}", fileUrl);
        }

        // 如果没有扩展名，根据类型设置默认扩展名
        if (StringUtils.isEmpty(extension))
        {
            switch (type.toLowerCase())
            {
                case "original":
                case "thumbnail":
                    extension = ".jpg";
                    break;
                case "model":
                    extension = ".glb";
                    break;
                default:
                    extension = ".bin";
            }
        }

        return title + "_" + type + extension;
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeByFileName(String fileName)
    {
        String fileType = FileTypeUtils.getFileType(fileName).toLowerCase();
        switch (fileType)
        {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "glb":
            case "gltf":
                return "model/gltf-binary";
            case "obj":
                return "application/octet-stream";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 修复URL中的localhost问题
     * 将localhost替换为实际的IP地址
     */
    private String fixLocalhostUrl(String originalUrl)
    {
        if (StringUtils.isEmpty(originalUrl))
        {
            return originalUrl;
        }

        // 如果URL包含localhost，替换为配置的Immich服务器地址
        if (originalUrl.contains("localhost:2283"))
        {
            // 使用配置文件中的Immich服务器地址
            return originalUrl.replace("localhost:2283", immichServerAddress);
        }

        return originalUrl;
    }

    /**
     * 获取当前用户的token
     */
    private String getCurrentUserToken()
    {
        try
        {
            // 从Spring Security上下文获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof LoginUser)
            {
                LoginUser loginUser = (LoginUser) authentication.getPrincipal();
                return loginUser.getToken();
            }
        }
        catch (Exception e)
        {
            log.warn("获取用户token失败: {}", e.getMessage());
        }
        return null;
    }

    @Override
    public boolean updateMaterialStatus(String materialId, String status, HttpServletRequest request)
    {
        try
        {
            // 构建更新状态的URL
            String updateUrl = inspirationSquareBaseUrl + "/external/api/inspiration/updateStatus";

            // 获取用户Token
            String token = request.getHeader("Token");
            if (StringUtils.isEmpty(token))
            {
                String authHeader = request.getHeader("Authorization");
                if (StringUtils.isNotEmpty(authHeader))
                {
                    if (authHeader.startsWith("Bearer "))
                    {
                        token = authHeader.substring(7);
                    }
                    else
                    {
                        token = authHeader;
                    }
                }
            }

            if (StringUtils.isEmpty(token))
            {
                throw new RuntimeException("Token验证失败");
            }

            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + token);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("materialId", materialId);
            requestBody.put("status", status);

            String requestBodyJson = JSON.toJSONString(requestBody);
            HttpEntity<String> entity = new HttpEntity<>(requestBodyJson, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(updateUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && StringUtils.isNotEmpty(response.getBody()))
            {
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                return jsonResponse.getInteger("code") == 200;
            }

            return false;
        }
        catch (Exception e)
        {
            log.error("更新素材状态失败，素材ID: {}, 状态: {}", materialId, status, e);
            throw new RuntimeException("更新状态失败: " + e.getMessage());
        }
    }
}
