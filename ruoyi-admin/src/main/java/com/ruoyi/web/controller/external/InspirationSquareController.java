package com.ruoyi.web.controller.external;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.domain.ExternalSystem;
import com.ruoyi.web.controller.external.domain.InspirationMaterial;
import com.ruoyi.web.controller.external.domain.InspirationCategory;
import com.ruoyi.web.controller.external.service.InspirationSquareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 灵感广场数据接口控制器
 * 
 * <AUTHOR>
 */
@Api("灵感广场数据接口")
@RestController
@RequestMapping("/external/api/inspiration")
public class InspirationSquareController extends BaseController
{
    @Autowired
    private InspirationSquareService inspirationSquareService;

    @Autowired
    private TokenService tokenService;

    /**
     * 获取素材分类列表
     */
    @ApiOperation("获取素材分类列表")
    @GetMapping("/categories")
    public AjaxResult getCategories(HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取素材分类列表", externalSystem.getSystemName());
        
        try
        {
            List<InspirationCategory> categories = inspirationSquareService.getCategories();
            return success(categories);
        }
        catch (Exception e)
        {
            logger.error("获取素材分类列表失败", e);
            return error("获取素材分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取素材列表
     */
    @ApiOperation("获取素材列表")
    @GetMapping("/materials")
    public TableDataInfo getMaterials(
            @ApiParam("分类ID") @RequestParam(required = false) Long categoryId,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("素材类型") @RequestParam(required = false) String materialType,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取素材列表，分类ID: {}, 关键词: {}, 类型: {}", 
                   externalSystem.getSystemName(), categoryId, keyword, materialType);
        
        try
        {
            startPage();
            List<InspirationMaterial> materials = inspirationSquareService.getMaterials(categoryId, keyword, materialType);
            return getDataTable(materials);
        }
        catch (Exception e)
        {
            logger.error("获取素材列表失败", e);
            return getDataTable(null);
        }
    }

    /**
     * 获取素材详情
     */
    @ApiOperation("获取素材详情")
    @GetMapping("/materials/{materialId}")
    public AjaxResult getMaterialDetail(
            @ApiParam("素材ID") @PathVariable Long materialId,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取素材详情，素材ID: {}", externalSystem.getSystemName(), materialId);
        
        try
        {
            InspirationMaterial material = inspirationSquareService.getMaterialById(materialId);
            if (material == null)
            {
                return error("素材不存在");
            }
            return success(material);
        }
        catch (Exception e)
        {
            logger.error("获取素材详情失败", e);
            return error("获取素材详情失败: " + e.getMessage());
        }
    }

    /**
     * 搜索素材
     */
    @ApiOperation("搜索素材")
    @PostMapping("/materials/search")
    @Log(title = "灵感广场", businessType = BusinessType.OTHER)
    public TableDataInfo searchMaterials(
            @RequestBody InspirationMaterial searchCriteria,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求搜索素材，搜索条件: {}", externalSystem.getSystemName(), searchCriteria);
        
        try
        {
            startPage();
            List<InspirationMaterial> materials = inspirationSquareService.searchMaterials(searchCriteria);
            return getDataTable(materials);
        }
        catch (Exception e)
        {
            logger.error("搜索素材失败", e);
            return getDataTable(null);
        }
    }

    /**
     * 获取热门素材
     */
    @ApiOperation("获取热门素材")
    @GetMapping("/materials/hot")
    public AjaxResult getHotMaterials(
            @ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取热门素材，限制数量: {}", externalSystem.getSystemName(), limit);
        
        try
        {
            List<InspirationMaterial> materials = inspirationSquareService.getHotMaterials(limit);
            return success(materials);
        }
        catch (Exception e)
        {
            logger.error("获取热门素材失败", e);
            return error("获取热门素材失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新素材
     */
    @ApiOperation("获取最新素材")
    @GetMapping("/materials/latest")
    public AjaxResult getLatestMaterials(
            @ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取最新素材，限制数量: {}", externalSystem.getSystemName(), limit);
        
        try
        {
            List<InspirationMaterial> materials = inspirationSquareService.getLatestMaterials(limit);
            return success(materials);
        }
        catch (Exception e)
        {
            logger.error("获取最新素材失败", e);
            return error("获取最新素材失败: " + e.getMessage());
        }
    }

    /**
     * 记录素材访问
     */
    @ApiOperation("记录素材访问")
    @PostMapping("/materials/{materialId}/visit")
    @Log(title = "灵感广场", businessType = BusinessType.OTHER)
    public AjaxResult recordMaterialVisit(
            @ApiParam("素材ID") @PathVariable Long materialId,
            HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 记录素材访问，素材ID: {}", externalSystem.getSystemName(), materialId);
        
        try
        {
            boolean success = inspirationSquareService.recordMaterialVisit(materialId, externalSystem.getSystemCode());
            if (success)
            {
                return success("访问记录成功");
            }
            else
            {
                return error("访问记录失败");
            }
        }
        catch (Exception e)
        {
            logger.error("记录素材访问失败", e);
            return error("记录素材访问失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统统计信息
     */
    @ApiOperation("获取系统统计信息")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(HttpServletRequest request)
    {
        ExternalSystem externalSystem = (ExternalSystem) request.getAttribute("externalSystem");
        logger.info("外部系统 {} 请求获取统计信息", externalSystem.getSystemName());
        
        try
        {
            Object statistics = inspirationSquareService.getStatistics(externalSystem.getSystemCode());
            return success(statistics);
        }
        catch (Exception e)
        {
            logger.error("获取统计信息失败", e);
            return error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取我的创作的所有素材详情
     */
    @ApiOperation("获取我的创作的所有素材详情")
    @GetMapping("/GetAllPersonDetail")
    public AjaxResult getAllPersonDetail(@ApiParam("用户Token") @RequestHeader("Token") String token, HttpServletRequest request)
    {
        try
        {
            // 验证Token
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null)
            {
                return AjaxResult.error(401, "Token无效或已过期");
            }

            // 调用外部灵感广场系统获取个人创作素材
            List<Map<String, Object>> dataList = inspirationSquareService.getPersonalMaterials(token);

            return AjaxResult.success("success", dataList);
        }
        catch (Exception e)
        {
            logger.error("获取个人创作素材失败", e);
            return AjaxResult.error("fail");
        }
    }

    /**
     * 获取灵感广场的所有素材详情
     */
    @ApiOperation("获取灵感广场的所有素材详情")
    @GetMapping("/GetAllContentDetail")
    public AjaxResult getAllContentDetail(
            @ApiParam("用户Token") @RequestHeader("Token") String token,
            @ApiParam("用户ID") @RequestParam("userId") String userId,
            @ApiParam("用户ID") @RequestParam("tag") String tag,
            HttpServletRequest request)
    {
        try
        {
            // 验证Token
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null)
            {
                return AjaxResult.error(401, "Token无效或已过期");
            }

            // 验证用户ID
            if (StringUtils.isEmpty(userId))
            {
                return AjaxResult.error(400, "用户ID不能为空");
            }

            // 调用外部灵感广场系统获取素材数据
            List<Map<String, Object>> dataList = inspirationSquareService.getInspirationMaterials(token, userId, tag);

            return AjaxResult.success("success", dataList);
        }
        catch (Exception e)
        {
            logger.error("获取灵感广场素材失败", e);
            return AjaxResult.error("fail");
        }
    }


}
