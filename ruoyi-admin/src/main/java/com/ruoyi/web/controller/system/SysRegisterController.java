package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysRegisterService;
import com.ruoyi.system.service.ISysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;

/**
 * 注册验证
 *
 * <AUTHOR>
 */
@Api("用户注册")
@RestController
public class SysRegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysConfigService configService;

    @ApiOperation("用户注册")
    @PostMapping("/register")
    public AjaxResult register(@Valid @RequestBody RegisterBody user)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }
        String msg = registerService.register(user);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }

    @ApiOperation("忘记密码")
    @PostMapping("/resetPassword")
    public AjaxResult resetPassword(@Valid @RequestBody RegisterBody user)
    {
        String phonenumber = user.getPhonenumber();
        String email = user.getEmail();
        SysUser sysUsers = sysUserService.selectUserByPhoneEmail(phonenumber,email);
        if (sysUsers==null)
        {
            return error("请检查输入信息！");
        }else {
            // 对密码进行加密处理
            String encryptedPassword = SecurityUtils.encryptPassword(user.getPassword());
            int i = sysUserService.resetUserPwd(sysUsers.getUserId(), encryptedPassword);
            return i>0?success():error("重置密码失败");
        }
    }

    //查询手机号是否存在
    @ApiOperation("查询手机号是否存在")
    @GetMapping ("/checkPhone")
    public AjaxResult checkPhone(@RequestParam String phonenumber)
    {
        SysUser sysUsers = sysUserService.selectUserByPhoneEmail(phonenumber,null);
        return sysUsers==null?success():error("手机号已存在");
    }

}
