package com.ruoyi.web.controller.external.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.web.controller.external.domain.InspirationMaterial;
import com.ruoyi.web.controller.external.domain.InspirationCategory;

/**
 * 灵感广场数据服务接口
 * 
 * <AUTHOR>
 */
public interface InspirationSquareService
{
    /**
     * 获取素材分类列表
     * 
     * @return 分类列表
     */
    List<InspirationCategory> getCategories();

    /**
     * 获取素材列表
     * 
     * @param categoryId 分类ID
     * @param keyword 关键词
     * @param materialType 素材类型
     * @return 素材列表
     */
    List<InspirationMaterial> getMaterials(Long categoryId, String keyword, String materialType);

    /**
     * 根据ID获取素材详情
     * 
     * @param materialId 素材ID
     * @return 素材详情
     */
    InspirationMaterial getMaterialById(Long materialId);

    /**
     * 搜索素材
     * 
     * @param searchCriteria 搜索条件
     * @return 素材列表
     */
    List<InspirationMaterial> searchMaterials(InspirationMaterial searchCriteria);

    /**
     * 获取热门素材
     * 
     * @param limit 数量限制
     * @return 热门素材列表
     */
    List<InspirationMaterial> getHotMaterials(Integer limit);

    /**
     * 获取最新素材
     * 
     * @param limit 数量限制
     * @return 最新素材列表
     */
    List<InspirationMaterial> getLatestMaterials(Integer limit);

    /**
     * 记录素材访问
     * 
     * @param materialId 素材ID
     * @param systemCode 系统编码
     * @return 是否成功
     */
    boolean recordMaterialVisit(Long materialId, String systemCode);

    /**
     * 获取统计信息
     *
     * @param systemCode 系统编码
     * @return 统计信息
     */
    Object getStatistics(String systemCode);

    /**
     * 获取个人创作的所有素材详情（调用外部系统）
     *
     * @param token 用户Token
     * @return 个人创作素材列表
     */
    List<Map<String, Object>> getPersonalMaterials(String token);

    /**
     * 获取灵感广场的所有素材详情（调用外部系统）
     *
     * @param token 用户Token
     * @param userId 用户ID
     * @return 灵感广场素材列表
     */
    List<Map<String, Object>> getInspirationMaterials(String token, String userId,String tag);

    /**
     * 上传图片素材到灵感广场系统
     *
     * @param file 上传的图片文件
     * @param title 素材标题
     * @param description 素材描述
     * @param tag 素材标签
     * @param status 发布状态：draft-草稿，published-已发布
     * @param request HTTP请求对象
     * @return 上传结果
     */
    Map<String, Object> uploadMaterial(org.springframework.web.multipart.MultipartFile file,
                                     String title, String description,
                                     String tag, String status, javax.servlet.http.HttpServletRequest request);

    /**
     * 上传模型文件到灵感广场系统
     *
     * @param file 模型预览图片
     * @param modelFile 模型文件
     * @param title 素材标题
     * @param description 素材描述
     * @param tag 素材标签
     * @param status 发布状态：draft-草稿，published-已发布
     * @param request HTTP请求对象
     * @return 上传结果
     */
    Map<String, Object> uploadModel(org.springframework.web.multipart.MultipartFile file,
                                  org.springframework.web.multipart.MultipartFile modelFile,
                                  String title, String description,
                                  String tag, String status, javax.servlet.http.HttpServletRequest request);

    /**
     * 下载素材文件
     *
     * @param materialId 素材ID
     * @param type 文件类型：original-原图，thumbnail-缩略图，model-模型文件
     * @param response HTTP响应对象
     */
    void downloadMaterial(String materialId, String type, javax.servlet.http.HttpServletResponse response);

    /**
     * 更新素材发布状态
     *
     * @param materialId 素材ID
     * @param status 发布状态：draft-草稿，published-已发布
     * @param request HTTP请求对象
     * @return 更新结果
     */
    boolean updateMaterialStatus(String materialId, String status, javax.servlet.http.HttpServletRequest request);
}
