package com.ruoyi.web.controller.external.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 灵感广场分类对象
 * 
 * <AUTHOR>
 */
@ApiModel("灵感广场分类")
public class InspirationCategory
{
    /** 分类ID */
    @ApiModelProperty("分类ID")
    private Long categoryId;

    /** 分类名称 */
    @ApiModelProperty("分类名称")
    private String categoryName;

    /** 分类编码 */
    @ApiModelProperty("分类编码")
    private String categoryCode;

    /** 父分类ID */
    @ApiModelProperty("父分类ID")
    private Long parentId;

    /** 分类层级 */
    @ApiModelProperty("分类层级")
    private Integer level;

    /** 分类路径 */
    @ApiModelProperty("分类路径")
    private String categoryPath;

    /** 分类描述 */
    @ApiModelProperty("分类描述")
    private String description;

    /** 分类图标 */
    @ApiModelProperty("分类图标")
    private String icon;

    /** 分类图片 */
    @ApiModelProperty("分类图片")
    private String image;

    /** 排序号 */
    @ApiModelProperty("排序号")
    private Integer sortOrder;

    /** 素材数量 */
    @ApiModelProperty("素材数量")
    private Long materialCount;

    /** 状态 */
    @ApiModelProperty("状态")
    private String status;

    /** 是否显示 */
    @ApiModelProperty("是否显示")
    private Boolean isVisible;

    /** 创建时间 */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 子分类列表 */
    @ApiModelProperty("子分类列表")
    private List<InspirationCategory> children;

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryCode()
    {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode)
    {
        this.categoryCode = categoryCode;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Integer getLevel()
    {
        return level;
    }

    public void setLevel(Integer level)
    {
        this.level = level;
    }

    public String getCategoryPath()
    {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath)
    {
        this.categoryPath = categoryPath;
    }

    public String getDescription()
    {
        return description;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getIcon()
    {
        return icon;
    }

    public void setIcon(String icon)
    {
        this.icon = icon;
    }

    public String getImage()
    {
        return image;
    }

    public void setImage(String image)
    {
        this.image = image;
    }

    public Integer getSortOrder()
    {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder)
    {
        this.sortOrder = sortOrder;
    }

    public Long getMaterialCount()
    {
        return materialCount;
    }

    public void setMaterialCount(Long materialCount)
    {
        this.materialCount = materialCount;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Boolean getIsVisible()
    {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible)
    {
        this.isVisible = isVisible;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public List<InspirationCategory> getChildren()
    {
        return children;
    }

    public void setChildren(List<InspirationCategory> children)
    {
        this.children = children;
    }

    @Override
    public String toString()
    {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("categoryCode", getCategoryCode())
            .append("parentId", getParentId())
            .append("level", getLevel())
            .append("categoryPath", getCategoryPath())
            .append("description", getDescription())
            .append("icon", getIcon())
            .append("image", getImage())
            .append("sortOrder", getSortOrder())
            .append("materialCount", getMaterialCount())
            .append("status", getStatus())
            .append("isVisible", getIsVisible())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("children", getChildren())
            .toString();
    }
}
