package com.ruoyi.web.controller.system;

import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.dto.UserProfileUpdateDto;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.controller.system.dto.SimpleUserProfileDto;
import com.ruoyi.framework.config.ServerConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@Api("个人信息管理")
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private TokenService tokenService;

    /**
     * 个人信息
     */
    @ApiOperation("获取个人信息")
    @GetMapping
    public AjaxResult profile()
    {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getUser();
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        return ajax;
    }

    /**
     * 简化的个人信息（只返回基本字段）
     */
    @ApiOperation("获取简化的个人信息")
    @GetMapping("/simple")
    public AjaxResult simpleProfile()
    {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getUser();

        // 处理头像URL，如果是相对路径则转换为完整URL
        String avatarUrl = user.getAvatar();
        if (StringUtils.isNotEmpty(avatarUrl) && !avatarUrl.startsWith("http"))
        {
            avatarUrl = serverConfig.getUrl() + avatarUrl;
        }

        // 创建简化的用户信息DTO
        SimpleUserProfileDto simpleUser = new SimpleUserProfileDto(
            user.getUserId(),
            user.getUserName(),
            user.getNickName(),
            user.getEmail(),
            user.getPhonenumber(),
            user.getSex(),
            avatarUrl,  // 使用完整的头像URL
            user.getStatus(),
            user.getDelFlag()
        );

        return AjaxResult.success(simpleUser);
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改个人信息")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user)
    {
        LoginUser loginUser = getLoginUser();
        SysUser currentUser = loginUser.getUser();
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhonenumber(user.getPhonenumber());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }
        if (userService.updateUserProfile(currentUser) > 0)
        {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @ApiOperation("修改密码")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(@RequestBody Map<String, String> params)
    {
        String oldPassword = params.get("oldPassword");
        String newPassword = params.get("newPassword");
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(userId, newPassword) > 0)
        {
            // 更新缓存用户密码&密码最后更新时间
            loginUser.getUser().setPwdUpdateDate(DateUtils.getNowDate());
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @ApiOperation("头像上传")
    @ApiImplicitParam(name = "avatarfile", value = "头像文件", required = true, dataType = "MultipartFile", dataTypeClass = MultipartFile.class)
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception
    {
        if (!file.isEmpty())
        {
            LoginUser loginUser = getLoginUser();
            String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION, true);
            if (userService.updateUserAvatar(loginUser.getUserId(), avatar))
            {
                String oldAvatar = loginUser.getUser().getAvatar();
                if (StringUtils.isNotEmpty(oldAvatar))
                {
                    FileUtils.deleteFile(RuoYiConfig.getProfile() + FileUtils.stripPrefix(oldAvatar));
                }
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }

    /**
     * 修改个人资料（昵称、邮箱、出生日期、个人简介）
     */
    @ApiOperation("修改个人资料")
    @Log(title = "个人资料", businessType = BusinessType.UPDATE)
    @GetMapping("/updateProfile")
    public AjaxResult updateUserProfile(
            @RequestParam(value = "nickName", required = false) String nickName,
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "birthDate", required = false) String birthDate,
            @RequestParam(value = "bio", required = false) String bio)
    {
        try {
            LoginUser loginUser = getLoginUser();
            SysUser currentUser = loginUser.getUser();

            // 参数验证和设置
            if (StringUtils.isNotEmpty(nickName)) {
                if (nickName.length() > 30) {
                    return error("用户昵称长度不能超过30个字符");
                }
                currentUser.setNickName(nickName);
            }

            if (StringUtils.isNotEmpty(email)) {
                if (email.length() > 50) {
                    return error("邮箱长度不能超过50个字符");
                }
                if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                    return error("邮箱格式不正确");
                }
                currentUser.setEmail(email);

                // 邮箱唯一性校验
                if (!userService.checkEmailUnique(currentUser)) {
                    return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
                }
            }

            if (StringUtils.isNotEmpty(birthDate)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    currentUser.setBirthDate(sdf.parse(birthDate));
                } catch (ParseException e) {
                    return error("出生日期格式不正确，请使用yyyy-MM-dd格式");
                }
            }

            if (bio != null) {
                if (bio.length() > 500) {
                    return error("个人简介长度不能超过500个字符");
                }
                currentUser.setBio(bio);
            }

            // 更新数据库
            if (userService.updateUserProfile(currentUser) > 0) {
                // 更新缓存用户信息
                tokenService.setLoginUser(loginUser);
                return success("个人资料修改成功");
            }
            return error("修改个人资料异常，请联系管理员");

        } catch (Exception e) {
            return error("修改个人资料失败：" + e.getMessage());
        }
    }

    /**
     * 获取个人资料详情（包含新增字段）
     */
    @ApiOperation("获取个人资料详情")
    @GetMapping("/detail")
    public AjaxResult getProfileDetail()
    {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getUser();

        // 构建返回数据
        Map<String, Object> profileData = new HashMap<>();
        profileData.put("userId", user.getUserId());
        profileData.put("userName", user.getUserName());
        profileData.put("nickName", user.getNickName());
        profileData.put("email", user.getEmail());
        profileData.put("phonenumber", user.getPhonenumber());
        profileData.put("sex", user.getSex());
        profileData.put("avatar", user.getAvatar());
        profileData.put("birthDate", user.getBirthDate());
        profileData.put("bio", user.getBio());
        profileData.put("createTime", user.getCreateTime());
        profileData.put("loginDate", user.getLoginDate());

        // 添加角色和岗位信息
        profileData.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        profileData.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));

        return AjaxResult.success(profileData);
    }
}
