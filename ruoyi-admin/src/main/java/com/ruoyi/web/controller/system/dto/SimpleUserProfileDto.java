package com.ruoyi.web.controller.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 简化的用户信息DTO
 * 
 * <AUTHOR>
 */
@ApiModel("简化的用户信息")
public class SimpleUserProfileDto
{
    /** 用户ID */
    @ApiModelProperty("用户ID")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    private String userName;

    /** 用户昵称 */
    @ApiModelProperty("用户昵称")
    private String nickName;

    /** 用户邮箱 */
    @ApiModelProperty("用户邮箱")
    private String email;

    /** 手机号码 */
    @ApiModelProperty("手机号码")
    private String phonenumber;

    /** 用户性别 */
    @ApiModelProperty("用户性别")
    private String sex;

    /** 头像地址 */
    @ApiModelProperty("头像地址")
    private String avatar;

    /** 帐号状态 */
    @ApiModelProperty("帐号状态")
    private String status;

    /** 删除标志 */
    @ApiModelProperty("删除标志")
    private String delFlag;

    public SimpleUserProfileDto()
    {
    }

    public SimpleUserProfileDto(Long userId, String userName, String nickName, String email, 
                               String phonenumber, String sex, String avatar, String status, String delFlag)
    {
        this.userId = userId;
        this.userName = userName;
        this.nickName = nickName;
        this.email = email;
        this.phonenumber = phonenumber;
        this.sex = sex;
        this.avatar = avatar;
        this.status = status;
        this.delFlag = delFlag;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    @Override
    public String toString()
    {
        return "SimpleUserProfileDto{" +
                "userId=" + userId +
                ", userName='" + userName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", email='" + email + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                ", sex='" + sex + '\'' +
                ", avatar='" + avatar + '\'' +
                ", status='" + status + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
