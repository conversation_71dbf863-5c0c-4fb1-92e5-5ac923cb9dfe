package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HelpCategory;
import com.ruoyi.system.domain.HelpDocument;
import com.ruoyi.system.service.IHelpService;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 帮助文档Controller
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Api("帮助文档管理")
@RestController
@RequestMapping("/help")
public class HelpController extends BaseController
{
    @Autowired
    private IHelpService helpService;

    /**
     * 获取帮助文档分类列表
     */
    @ApiOperation("获取帮助文档分类列表")
    @GetMapping("/categories")
    public AjaxResult getCategories()
    {
        List<HelpCategory> list = helpService.selectHelpCategoryList(new HelpCategory());
        return AjaxResult.success(list);
    }

    /**
     * 获取帮助文档列表（按分类分组）
     */
    @ApiOperation("获取帮助文档列表")
    @GetMapping("/documents")
    public AjaxResult getDocuments(
            @ApiParam("分类ID") @RequestParam(value = "categoryId", required = false) Long categoryId,
            @ApiParam("搜索关键词") @RequestParam(value = "keyword", required = false) String keyword)
    {
        Map<String, Object> result = helpService.getDocumentsGroupByCategory(categoryId, keyword);
        return AjaxResult.success(result);
    }

    /**
     * 获取帮助文档详情
     */
    @ApiOperation("获取帮助文档详情")
    @GetMapping("/document/{id}")
    public AjaxResult getDocumentDetail(@ApiParam("文档ID") @PathVariable("id") Long id)
    {
        HelpDocument document = helpService.selectHelpDocumentById(id);
        if (document != null)
        {
            // 增加查看次数
            helpService.incrementViewCount(id);
        }
        return AjaxResult.success(document);
    }

    /**
     * 搜索帮助文档
     */
    @ApiOperation("搜索帮助文档")
    @GetMapping("/search")
    public AjaxResult searchDocuments(
            @ApiParam("搜索关键词") @RequestParam("keyword") String keyword,
            @ApiParam("页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize)
    {
        startPage();
        List<HelpDocument> list = helpService.searchDocuments(keyword);
        return AjaxResult.success(getDataTable(list));
    }

    /**
     * 获取热门文档
     */
    @ApiOperation("获取热门文档")
    @GetMapping("/hot")
    public AjaxResult getHotDocuments(
            @ApiParam("数量限制") @RequestParam(value = "limit", defaultValue = "10") Integer limit)
    {
        List<HelpDocument> list = helpService.getHotDocuments(limit);
        return AjaxResult.success(list);
    }

    // ==================== 管理端接口 ====================

    /**
     * 查询帮助文档分类列表
     */
    @ApiOperation("查询帮助文档分类列表")
    @PreAuthorize("@ss.hasPermi('system:help:list')")
    @GetMapping("/category/list")
    public TableDataInfo categoryList(HelpCategory helpCategory)
    {
        startPage();
        List<HelpCategory> list = helpService.selectHelpCategoryList(helpCategory);
        return getDataTable(list);
    }

    /**
     * 新增帮助文档分类
     */
    @ApiOperation("新增帮助文档分类")
    @PreAuthorize("@ss.hasPermi('system:help:add')")
    @Log(title = "帮助文档分类", businessType = BusinessType.INSERT)
    @PostMapping("/category")
    public AjaxResult addCategory(@RequestBody HelpCategory helpCategory)
    {
        return toAjax(helpService.insertHelpCategory(helpCategory));
    }

    /**
     * 修改帮助文档分类
     */
    @ApiOperation("修改帮助文档分类")
    @PreAuthorize("@ss.hasPermi('system:help:edit')")
    @Log(title = "帮助文档分类", businessType = BusinessType.UPDATE)
    @PutMapping("/category")
    public AjaxResult editCategory(@RequestBody HelpCategory helpCategory)
    {
        return toAjax(helpService.updateHelpCategory(helpCategory));
    }

    /**
     * 删除帮助文档分类
     */
    @ApiOperation("删除帮助文档分类")
    @PreAuthorize("@ss.hasPermi('system:help:remove')")
    @Log(title = "帮助文档分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/category/{ids}")
    public AjaxResult removeCategory(@PathVariable Long[] ids)
    {
        return toAjax(helpService.deleteHelpCategoryByIds(ids));
    }

    /**
     * 查询帮助文档列表
     */
    @ApiOperation("查询帮助文档列表")
    @PreAuthorize("@ss.hasPermi('system:help:list')")
    @GetMapping("/document/list")
    public TableDataInfo documentList(HelpDocument helpDocument)
    {
        startPage();
        List<HelpDocument> list = helpService.selectHelpDocumentList(helpDocument);
        return getDataTable(list);
    }

    /**
     * 获取帮助文档详细信息
     */
    @ApiOperation("获取帮助文档详细信息")
    @PreAuthorize("@ss.hasPermi('system:help:query')")
    @GetMapping("/document/detail/{id}")
    public AjaxResult getDocumentInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(helpService.selectHelpDocumentById(id));
    }

    /**
     * 新增帮助文档
     */
    @ApiOperation("新增帮助文档")
    @PreAuthorize("@ss.hasPermi('system:help:add')")
    @Log(title = "帮助文档", businessType = BusinessType.INSERT)
    @PostMapping("/document")
    public AjaxResult addDocument(@RequestBody HelpDocument helpDocument)
    {
        return toAjax(helpService.insertHelpDocument(helpDocument));
    }

    /**
     * 修改帮助文档
     */
    @ApiOperation("修改帮助文档")
    @PreAuthorize("@ss.hasPermi('system:help:edit')")
    @Log(title = "帮助文档", businessType = BusinessType.UPDATE)
    @PutMapping("/document")
    public AjaxResult editDocument(@RequestBody HelpDocument helpDocument)
    {
        return toAjax(helpService.updateHelpDocument(helpDocument));
    }

    /**
     * 删除帮助文档
     */
    @ApiOperation("删除帮助文档")
    @PreAuthorize("@ss.hasPermi('system:help:remove')")
    @Log(title = "帮助文档", businessType = BusinessType.DELETE)
    @DeleteMapping("/document/{ids}")
    public AjaxResult removeDocument(@PathVariable Long[] ids)
    {
        return toAjax(helpService.deleteHelpDocumentByIds(ids));
    }
}
