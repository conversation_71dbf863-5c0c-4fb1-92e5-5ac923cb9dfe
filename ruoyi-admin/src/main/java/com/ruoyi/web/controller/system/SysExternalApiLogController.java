package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ExternalApiLog;
import com.ruoyi.system.service.IExternalApiLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 外部API调用日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api("外部API调用日志管理")
@RestController
@RequestMapping("/system/external/log")
public class SysExternalApiLogController extends BaseController
{
    @Autowired
    private IExternalApiLogService externalApiLogService;

    /**
     * 查询外部API调用日志列表
     */
    @ApiOperation("查询外部API调用日志列表")
    @PreAuthorize("@ss.hasPermi('system:external:log')")
    @GetMapping("/list")
    public TableDataInfo list(ExternalApiLog externalApiLog)
    {
        startPage();
        List<ExternalApiLog> list = externalApiLogService.selectExternalApiLogList(externalApiLog);
        return getDataTable(list);
    }

    /**
     * 导出外部API调用日志列表
     */
    @ApiOperation("导出外部API调用日志列表")
    @PreAuthorize("@ss.hasPermi('system:external:logExport')")
    @Log(title = "外部API调用日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExternalApiLog externalApiLog)
    {
        List<ExternalApiLog> list = externalApiLogService.selectExternalApiLogList(externalApiLog);
        ExcelUtil<ExternalApiLog> util = new ExcelUtil<ExternalApiLog>(ExternalApiLog.class);
        util.exportExcel(response, list, "外部API调用日志数据");
    }

    /**
     * 获取外部API调用日志详细信息
     */
    @ApiOperation("获取外部API调用日志详细信息")
    @PreAuthorize("@ss.hasPermi('system:external:log')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@ApiParam("日志ID") @PathVariable("logId") Long logId)
    {
        return success(externalApiLogService.selectExternalApiLogByLogId(logId));
    }

    /**
     * 删除外部API调用日志
     */
    @ApiOperation("删除外部API调用日志")
    @PreAuthorize("@ss.hasPermi('system:external:logRemove')")
    @Log(title = "外部API调用日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@ApiParam("日志ID数组") @PathVariable Long[] logIds)
    {
        return toAjax(externalApiLogService.deleteExternalApiLogByLogIds(logIds));
    }

    /**
     * 清理指定天数前的日志
     */
    @ApiOperation("清理指定天数前的日志")
    @PreAuthorize("@ss.hasPermi('system:external:logRemove')")
    @Log(title = "外部API调用日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean/{days}")
    public AjaxResult clean(@ApiParam("保留天数") @PathVariable Integer days)
    {
        if (days < 1)
        {
            return error("保留天数必须大于0");
        }
        
        int result = externalApiLogService.cleanExternalApiLogByDays(days);
        return success("成功清理 " + result + " 条日志记录");
    }

    /**
     * 统计API调用次数
     */
    @ApiOperation("统计API调用次数")
    @PreAuthorize("@ss.hasPermi('system:external:log')")
    @GetMapping("/count")
    public AjaxResult count(ExternalApiLog externalApiLog)
    {
        Long count = externalApiLogService.countExternalApiLog(externalApiLog);
        return success(count);
    }

    /**
     * 统计API调用成功率
     */
    @ApiOperation("统计API调用成功率")
    @PreAuthorize("@ss.hasPermi('system:external:log')")
    @GetMapping("/successRate")
    public AjaxResult successRate(@ApiParam("系统ID") Long systemId, 
                                 @ApiParam("开始时间") String startTime, 
                                 @ApiParam("结束时间") String endTime)
    {
        List<ExternalApiLog> statistics = externalApiLogService.selectApiSuccessRate(systemId, startTime, endTime);
        return success(statistics);
    }
}
