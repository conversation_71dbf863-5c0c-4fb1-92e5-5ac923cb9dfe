18:22:42.250 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.updateUser - [debug,135] - ==>  Preparing: update sys_user SET login_ip = ?, login_date = ?, update_time = sysdate() where user_id = ?
18:22:42.252 [schedule-pool-1] INFO  sys-user - [run,55] - [*************]内网IP[admin][Success][登录成功]
18:22:42.254 [schedule-pool-1] DEBUG c.r.s.m.S.insertLogininfor - [debug,135] - ==>  Preparing: insert into sys_logininfor (user_name, status, ipaddr, login_location, browser, os, msg, login_time) values (?, ?, ?, ?, ?, ?, ?, sysdate())
18:22:42.256 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.updateUser - [debug,135] - ==> Parameters: *************(String), 2025-08-14 18:22:42.246(Timestamp), 1(Long)
18:22:42.289 [http-nio-8080-exec-1] DEBUG c.r.s.m.S.updateUser - [debug,135] - <==    Updates: 1
18:22:42.410 [schedule-pool-1] DEBUG c.r.s.m.S.insertLogininfor - [debug,135] - ==> Parameters: admin(String), 0(String), *************(String), 内网IP(String), Unknown(String), Unknown(String), 登录成功(String)
18:22:42.432 [schedule-pool-1] DEBUG c.r.s.m.S.insertLogininfor - [debug,135] - <==    Updates: 1
18:23:01.713 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,487] - 准备调用Immich接口: http://*************:2283/api/external/api/inspiration/GetAllContentDetail?userId=admin
18:23:01.713 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,488] - 使用Token: system_token
18:23:01.714 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,496] - 发送请求头: [Authorization:"Bearer system_token", Content-Type:"application/json"]
18:23:01.743 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,500] - 响应状态: 200 OK
18:23:01.743 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,501] - 响应头: [X-Powered-By:"Express", Access-Control-Allow-Origin:"*", x-immich-cid:"3bi7c10h", Content-Type:"application/json; charset=utf-8", Content-Length:"13602", ETag:""3522-MDo2G2LbUYDs/7v4+htp9AEi9Ug"", Date:"Thu, 14 Aug 2025 10:23:00 GMT", Connection:"keep-alive", Keep-Alive:"timeout=5"]
18:23:01.744 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [getInspirationMaterials,502] - 响应体: {"code":200,"msg":"success","tagnum":3,"data":[{"categories":["图片","portrait","视频"],"图片":[{"guidId":"inspiration_0fa08f13-bf5b-448e-9c3c-6fe0c9e221f3","type":"image","title":"鲨鱼","thumbnail":"http://localhost:2283/api/assets/0fa08f13-bf5b-448e-9c3c-6fe0c9e221f3/thumbnail","filepath":"http://localhost:2283/api/assets/0fa08f13-bf5b-448e-9c3c-6fe0c9e221f3/original","createdTime":"2025-08-14T01:28:59.952Z","modifiedTime":"2025-08-14T01:29:00.381Z"},{"guidId":"inspiration_f8539f3e-86f5-481f-b4af-865898bab3bf","type":"image","title":"å¾®ä¿¡å¾ç_20250618142056.jpg","thumbnail":"http://localhost:2283/api/assets/f8539f3e-86f5-481f-b4af-865898bab3bf/thumbnail","filepath":"http://localhost:2283/api/assets/f8539f3e-86f5-481f-b4af-865898bab3bf/original","createdTime":"2025-08-13T09:18:38.569Z","modifiedTime":"2025-08-13T09:18:38.810Z"},{"guidId":"inspiration_60e2769e-c2f8-4ee8-899b-84c35b3d966d","type":"image","title":"chiwei 陶瓷004 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/60e2769e-c2f8-4ee8-899b-84c35b3d966d/thumbnail","filepath":"http://localhost:2283/api/assets/60e2769e-c2f8-4ee8-899b-84c35b3d966d/original","createdTime":"2025-07-01T03:29:43.830Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_0ce1ac96-c456-49f6-a3e6-f47b41b10ea6","type":"image","title":"skdmuseum 陶瓷009 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/thumbnail","filepath":"http://localhost:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/original","createdTime":"2025-07-01T03:29:44.704Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_26d4edff-2bcc-4898-8f59-c1ac32eaece9","type":"image","title":"福绣6x9.jpg","thumbnail":"http://localhost:2283/api/assets/26d4edff-2bcc-4898-8f59-c1ac32eaece9/thumbnail","filepath":"http://localhost:2283/api/assets/26d4edff-2bcc-4898-8f59-c1ac32eaece9/original","createdTime":"2025-07-01T03:29:47.747Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_d7453883-b6de-4eab-8d23-0bf03ba896eb","type":"image","title":"汉服文字6x9.jpg","thumbnail":"http://localhost:2283/api/assets/d7453883-b6de-4eab-8d23-0bf03ba896eb/thumbnail","filepath":"http://localhost:2283/api/assets/d7453883-b6de-4eab-8d23-0bf03ba896eb/original","createdTime":"2025-07-01T03:29:50.147Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_14ceb317-201b-4a2a-b8f6-dda74e28ae62","type":"image","title":"凉亭文字6x9.jpg","thumbnail":"http://localhost:2283/api/assets/14ceb317-201b-4a2a-b8f6-dda74e28ae62/thumbnail","filepath":"http://localhost:2283/api/assets/14ceb317-201b-4a2a-b8f6-dda74e28ae62/original","createdTime":"2025-07-01T03:29:50.455Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_1e39fd24-b499-418a-ab14-59f2c01d4b3e","type":"image","title":"千里江山图上下6x9.jpg","thumbnail":"http://localhost:2283/api/assets/1e39fd24-b499-418a-ab14-59f2c01d4b3e/thumbnail","filepath":"http://localhost:2283/api/assets/1e39fd24-b499-418a-ab14-59f2c01d4b3e/original","createdTime":"2025-07-01T03:29:55.143Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_5e02eafb-fd80-412c-bbc8-d4319d3aaf41","type":"image","title":"清明上河图006 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/5e02eafb-fd80-412c-bbc8-d4319d3aaf41/thumbnail","filepath":"http://localhost:2283/api/assets/5e02eafb-fd80-412c-bbc8-d4319d3aaf41/original","createdTime":"2025-07-01T03:29:56.527Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_f4b48afc-05a7-425f-b9e6-80d4e18d9b82","type":"image","title":"舞狮004 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/f4b48afc-05a7-425f-b9e6-80d4e18d9b82/thumbnail","filepath":"http://localhost:2283/api/assets/f4b48afc-05a7-425f-b9e6-80d4e18d9b82/original","createdTime":"2025-07-01T03:29:59.398Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_e8d35479-26ff-48ba-973a-ccb009ebe05a","type":"image","title":"玉雕龙002 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/e8d35479-26ff-48ba-973a-ccb009ebe05a/thumbnail","filepath":"http://localhost:2283/api/assets/e8d35479-26ff-48ba-973a-ccb009ebe05a/original","createdTime":"2025-07-01T03:30:01.077Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_29b5e942-20ab-4daa-9e52-d66525d69a35","type":"image","title":"022.jpeg","thumbnail":"http://localhost:2283/api/assets/29b5e942-20ab-4daa-9e52-d66525d69a35/thumbnail","filepath":"http://localhost:2283/api/assets/29b5e942-20ab-4daa-9e52-d66525d69a35/original","createdTime":"2025-07-01T03:29:22.392Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_39f0e199-e006-4004-a1eb-27ff18d85ba6","type":"image","title":"0111.jpeg","thumbnail":"http://localhost:2283/api/assets/39f0e199-e006-4004-a1eb-27ff18d85ba6/thumbnail","filepath":"http://localhost:2283/api/assets/39f0e199-e006-4004-a1eb-27ff18d85ba6/original","createdTime":"2025-07-01T03:29:22.478Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_a2423373-ea01-48fe-ac0c-d3341902f03e","type":"image","title":"bond.jpg","thumbnail":"http://localhost:2283/api/assets/a2423373-ea01-48fe-ac0c-d3341902f03e/thumbnail","filepath":"http://localhost:2283/api/assets/a2423373-ea01-48fe-ac0c-d3341902f03e/original","createdTime":"2025-07-01T03:29:23.211Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_4335d6f2-11c7-423a-9ee1-cde40b1a107e","type":"image","title":"bond-5x9.jpg","thumbnail":"http://localhost:2283/api/assets/4335d6f2-11c7-423a-9ee1-cde40b1a107e/thumbnail","filepath":"http://localhost:2283/api/assets/4335d6f2-11c7-423a-9ee1-cde40b1a107e/original","createdTime":"2025-07-01T03:29:23.473Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_8dbab10b-62c9-490c-876f-395fcfcac481","type":"image","title":"logo.png","thumbnail":"http://localhost:2283/api/assets/8dbab10b-62c9-490c-876f-395fcfcac481/thumbnail","filepath":"http://localhost:2283/api/assets/8dbab10b-62c9-490c-876f-395fcfcac481/original","createdTime":"2025-07-01T03:29:25.556Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_1885a378-5e23-4fa4-b1f4-11f94e872f05","type":"image","title":"luori.jpeg","thumbnail":"http://localhost:2283/api/assets/1885a378-5e23-4fa4-b1f4-11f94e872f05/thumbnail","filepath":"http://localhost:2283/api/assets/1885a378-5e23-4fa4-b1f4-11f94e872f05/original","createdTime":"2025-07-01T03:29:25.867Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_d53fdd32-05dc-4d4a-b93a-387c7e3e35bb","type":"image","title":"大雁.jpg","thumbnail":"http://localhost:2283/api/assets/d53fdd32-05dc-4d4a-b93a-387c7e3e35bb/thumbnail","filepath":"http://localhost:2283/api/assets/d53fdd32-05dc-4d4a-b93a-387c7e3e35bb/original","createdTime":"2025-07-01T03:29:27.490Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_e7d38c21-**************-86b5f0b0232f","type":"image","title":"粉色.jpeg","thumbnail":"http://localhost:2283/api/assets/e7d38c21-**************-86b5f0b0232f/thumbnail","filepath":"http://localhost:2283/api/assets/e7d38c21-**************-86b5f0b0232f/original","createdTime":"2025-07-01T03:29:27.735Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_ca9a148f-**************-12a71f386738","type":"image","title":"小菊花.jpg","thumbnail":"http://localhost:2283/api/assets/ca9a148f-**************-12a71f386738/thumbnail","filepath":"http://localhost:2283/api/assets/ca9a148f-**************-12a71f386738/original","createdTime":"2025-07-01T03:29:29.666Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_94afc345-02c0-4770-bd16-5f30da5cc766","type":"image","title":"face008 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/94afc345-02c0-4770-bd16-5f30da5cc766/thumbnail","filepath":"http://localhost:2283/api/assets/94afc345-02c0-4770-bd16-5f30da5cc766/original","createdTime":"2025-07-01T03:30:10.356Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_70ecc8f4-806c-46f6-a5fa-b1b8be2f38c2","type":"image","title":"半身（黄花)5x9.jpg","thumbnail":"http://localhost:2283/api/assets/70ecc8f4-806c-46f6-a5fa-b1b8be2f38c2/thumbnail","filepath":"http://localhost:2283/api/assets/70ecc8f4-806c-46f6-a5fa-b1b8be2f38c2/original","createdTime":"2025-07-01T03:30:12.958Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_5f6d6853-98de-478c-87f1-eded51f5dafb","type":"image","title":"比基尼半生(花).jpg","thumbnail":"http://localhost:2283/api/assets/5f6d6853-98de-478c-87f1-eded51f5dafb/thumbnail","filepath":"http://localhost:2283/api/assets/5f6d6853-98de-478c-87f1-eded51f5dafb/original","createdTime":"2025-07-01T03:30:13.688Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_5ab9400d-8ff9-4d17-95a6-ca4b59c08166","type":"image","title":"比基尼女（花）5x9.jpg","thumbnail":"http://localhost:2283/api/assets/5ab9400d-8ff9-4d17-95a6-ca4b59c08166/thumbnail","filepath":"http://localhost:2283/api/assets/5ab9400d-8ff9-4d17-95a6-ca4b59c08166/original","createdTime":"2025-07-01T03:30:14.940Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_c0e2b022-8d1b-47dc-9f18-401281a0459b","type":"image","title":"比基尼女（黄花)5x9.jpg","thumbnail":"http://localhost:2283/api/assets/c0e2b022-8d1b-47dc-9f18-401281a0459b/thumbnail","filepath":"http://localhost:2283/api/assets/c0e2b022-8d1b-47dc-9f18-401281a0459b/original","createdTime":"2025-07-01T03:30:16.351Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_88930df9-0e0b-4e10-904b-a2ae52520465","type":"image","title":"59964ff6bb99b84da230258293ec3559.jpg","thumbnail":"http://localhost:2283/api/assets/88930df9-0e0b-4e10-904b-a2ae52520465/thumbnail","filepath":"http://localhost:2283/api/assets/88930df9-0e0b-4e10-904b-a2ae52520465/original","createdTime":"2025-08-13T08:42:53.064Z","modifiedTime":"2025-08-13T08:42:53.676Z"},{"guidId":"inspiration_d2dddef5-43cb-4c1c-bb08-b6479d23319b","type":"image","title":"鲨鱼2","thumbnail":"http://localhost:2283/api/assets/d2dddef5-43cb-4c1c-bb08-b6479d23319b/thumbnail","filepath":"http://localhost:2283/api/assets/d2dddef5-43cb-4c1c-bb08-b6479d23319b/original","createdTime":"2025-08-13T10:48:47.803Z","modifiedTime":"2025-08-13T10:48:48.161Z"}],"portrait":[{"guidId":"inspiration_7f073c92-0c75-454a-a4c5-21c3407e7db4","type":"image","title":"face004 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/7f073c92-0c75-454a-a4c5-21c3407e7db4/thumbnail","filepath":"http://localhost:2283/api/assets/7f073c92-0c75-454a-a4c5-21c3407e7db4/original","createdTime":"2025-07-01T03:30:01.671Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_a79cdbe0-e03f-4ec2-82c9-36629beff58f","type":"image","title":"face6x9.jpg","thumbnail":"http://localhost:2283/api/assets/a79cdbe0-e03f-4ec2-82c9-36629beff58f/thumbnail","filepath":"http://localhost:2283/api/assets/a79cdbe0-e03f-4ec2-82c9-36629beff58f/original","createdTime":"2025-07-01T03:29:24.848Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_0afcd9f8-e986-45eb-9ae4-6c2815f769aa","type":"image","title":"face005 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/0afcd9f8-e986-45eb-9ae4-6c2815f769aa/thumbnail","filepath":"http://localhost:2283/api/assets/0afcd9f8-e986-45eb-9ae4-6c2815f769aa/original","createdTime":"2025-07-01T03:30:05.451Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_f20a94c3-7721-4227-bdc1-4e9bf71d50fc","type":"image","title":"face006 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/f20a94c3-7721-4227-bdc1-4e9bf71d50fc/thumbnail","filepath":"http://localhost:2283/api/assets/f20a94c3-7721-4227-bdc1-4e9bf71d50fc/original","createdTime":"2025-07-01T03:30:06.035Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_715e3dfe-eb1a-482c-a257-9c1a8c19985c","type":"image","title":"face007 6x9.jpg","thumbnail":"http://localhost:2283/api/assets/715e3dfe-eb1a-482c-a257-9c1a8c19985c/thumbnail","filepath":"http://localhost:2283/api/assets/715e3dfe-eb1a-482c-a257-9c1a8c19985c/original","createdTime":"2025-07-01T03:30:09.770Z","modifiedTime":"2025-08-12T01:25:25.144Z"}],"视频":[{"guidId":"inspiration_08a56d6e-d5a1-4da3-aa24-722f7c44e03e","type":"video","title":"皮影002 6x9.mp4","thumbnail":"http://localhost:2283/api/assets/08a56d6e-d5a1-4da3-aa24-722f7c44e03e/thumbnail","filepath":"http://localhost:2283/api/assets/08a56d6e-d5a1-4da3-aa24-722f7c44e03e/original","createdTime":"2025-07-01T03:29:30.213Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_0abcdc29-ec02-46d3-b34b-a972cd10bcbc","type":"video","title":"变脸002 5x9.mp4","thumbnail":"http://localhost:2283/api/assets/0abcdc29-ec02-46d3-b34b-a972cd10bcbc/thumbnail","filepath":"http://localhost:2283/api/assets/0abcdc29-ec02-46d3-b34b-a972cd10bcbc/original","createdTime":"2025-07-01T03:29:48.688Z","modifiedTime":"2025-08-12T01:25:25.143Z"},{"guidId":"inspiration_ed20965d-cc97-454a-9727-cb4d7b3579ca","type":"video","title":"candy-5x9.mp4","thumbnail":"http://localhost:2283/api/assets/ed20965d-cc97-454a-9727-cb4d7b3579ca/thumbnail","filepath":"http://localhost:2283/api/assets/ed20965d-cc97-454a-9727-cb4d7b3579ca/original","createdTime":"2025-07-01T03:29:24.481Z","modifiedTime":"2025-08-12T01:25:25.144Z"},{"guidId":"inspiration_43a3d1c5-ad7a-4be6-ac95-1314b39f4233","type":"video","title":"蛇5x9.mp4","thumbnail":"http://localhost:2283/api/assets/43a3d1c5-ad7a-4be6-ac95-1314b39f4233/thumbnail","filepath":"http://localhost:2283/api/assets/43a3d1c5-ad7a-4be6-ac95-1314b39f4233/original","createdTime":"2025-07-01T03:29:59.387Z","modifiedTime":"2025-08-12T01:25:25.144Z"}]}]}
18:23:01.747 [http-nio-8080-exec-5] INFO  c.r.w.c.e.s.i.InspirationSquareServiceImpl - [downloadAndTransferFile,1055] - 原始URL: http://localhost:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/original, 修复后URL: http://*************:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/original
18:23:01.759 [http-nio-8080-exec-5] ERROR c.r.w.c.e.s.i.InspirationSquareServiceImpl - [downloadMaterial,954] - 下载素材文件失败，素材ID: inspiration_0ce1ac96-c456-49f6-a3e6-f47b41b10ea6, 类型: original
java.io.IOException: Server returned HTTP response code: 401 for URL: http://*************:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/original
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2071)
	at java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2066)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2065)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1635)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1615)
	at com.ruoyi.web.controller.external.service.impl.InspirationSquareServiceImpl.downloadAndTransferFile(InspirationSquareServiceImpl.java:1083)
	at com.ruoyi.web.controller.external.service.impl.InspirationSquareServiceImpl.downloadMaterial(InspirationSquareServiceImpl.java:950)
	at com.ruoyi.web.controller.common.InspirationController.downloadMaterial(InspirationController.java:180)
	at com.ruoyi.web.controller.common.InspirationController$$FastClassBySpringCGLIB$$5594b33c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.security.authorization.method.AuthorizationManagerBeforeMethodInterceptor.invoke(AuthorizationManagerBeforeMethodInterceptor.java:162)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ruoyi.web.controller.common.InspirationController$$EnhancerBySpringCGLIB$$3ed12ab3.downloadMaterial(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.ruoyi.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:39)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.ruoyi.framework.security.filter.ExternalApiAuthenticationFilter.doFilterInternal(ExternalApiAuthenticationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.io.IOException: Server returned HTTP response code: 401 for URL: http://*************:2283/api/assets/0ce1ac96-c456-49f6-a3e6-f47b41b10ea6/original
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2014)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1615)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getHeaderField(HttpURLConnection.java:3251)
	at java.base/java.net.URLConnection.getContentType(URLConnection.java:522)
	at com.ruoyi.web.controller.external.service.impl.InspirationSquareServiceImpl.downloadAndTransferFile(InspirationSquareServiceImpl.java:1064)
	... 123 common frames omitted


实际请求：curl --location --request GET 'http://*************:8080/inspiration/download/inspiration_0ce1ac96-c456-49f6-a3e6-f47b41b10ea6?type=original' \
     --header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
     --header 'Authorization: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiMzcxYzVjYzctNTlkMS00MTAwLWJiMWMtOTkwZGU3NDczMTQ3In0.wkX50bTj7ecJ1ExMayeQhy0VOMn5QlZ49P7_kQNIYdRJ1L8nTao2gL7HbKlhSIzHLeZokbkGouMwBfP6Y3ypGQ' \
     --header 'Accept: */*' \
     --header 'Host: *************:8080' \
     --header 'Connection: keep-alive'

调用日志：{
         "code": 500,
         "msg": "下载失败: Connection refused: getsockopt"
     }



素材广场日志：2025-08-14 18:16:19 收到灵感广场请求: {
       2025-08-14 18:16:19   authorization: 'Bearer system_token',
       2025-08-14 18:16:19   dto: GetAllContentDetailDto { userId: undefined, tag: undefined }
       2025-08-14 18:16:19 }
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] 收到灵感广场请求
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] Object(3) {
       2025-08-14 18:16:19   authorization: 'Bearer system_token',
       2025-08-14 18:16:19   dto: GetAllContentDetailDto {
       2025-08-14 18:16:19     userId: undefined,
       2025-08-14 18:16:19     tag: undefined
       2025-08-14 18:16:19   },
       2025-08-14 18:16:19   tag: undefined
       2025-08-14 18:16:19 }
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM    WARN [Api:InspirationService~e2yzwgcl] Token验证失败，但继续查询数据库
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM    WARN [Api:InspirationService~e2yzwgcl] 无效的Token
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] 开始查询数据库中的灵感广场素材
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] 查询到 36 个素材ID
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] 获取到 36 个包含标签的素材
       2025-08-14 18:16:19 [Nest] 38  - 08/14/2025, 10:16:19 AM     LOG [Api:InspirationService~e2yzwgcl] 返回 3 个分类的灵感广场素材
       2025-08-14 18:16:55 收到灵感广场请求: {
       2025-08-14 18:16:55   authorization: 'Bearer system_token',
       2025-08-14 18:16:55   dto: GetAllContentDetailDto { userId: 'admin', tag: undefined }
       2025-08-14 18:16:55 }
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] 收到灵感广场请求
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] Object(3) {
       2025-08-14 18:16:55   authorization: 'Bearer system_token',
       2025-08-14 18:16:55   dto: GetAllContentDetailDto {
       2025-08-14 18:16:55     userId: 'admin',
       2025-08-14 18:16:55     tag: undefined
       2025-08-14 18:16:55   },
       2025-08-14 18:16:55   tag: undefined
       2025-08-14 18:16:55 }
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM    WARN [Api:InspirationService~xwl3ez27] Token验证失败，但继续查询数据库
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM    WARN [Api:InspirationService~xwl3ez27] 无效的Token
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] 开始查询数据库中的灵感广场素材
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] 查询到 36 个素材ID
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] 获取到 36 个包含标签的素材
       2025-08-14 18:16:55 [Nest] 38  - 08/14/2025, 10:16:55 AM     LOG [Api:InspirationService~xwl3ez27] 返回 3 个分类的灵感广场素材



