# 灵感广场数据打通系统部署指南

## 重要说明
本指南分为两部分：
- **第1-11章**：本系统（RuoYi系统）的部署步骤
- **第12章**：外部系统（灵感广场系统）需要的配置

---

## 本系统部署（RuoYi系统）

## 1. 环境准备

### 1.1 系统要求
- JDK 1.8+
- MySQL 5.7+
- Redis 3.0+
- Maven 3.6+

### 1.2 依赖检查
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查MySQL服务
mysql --version

# 检查Redis服务
redis-cli ping
```

## 2. 数据库初始化

### 2.1 创建数据库
```sql
CREATE DATABASE IF NOT EXISTS cjviewer DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cjviewer;
```

### 2.2 执行初始化脚本
```bash
# 执行外部系统相关表的创建脚本
mysql -u root -p cjviewer < sql/external_system_tables.sql
```

### 2.3 验证表创建
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'external_%';

-- 检查初始数据
SELECT * FROM external_system;
SELECT * FROM sys_menu WHERE menu_name LIKE '%外部%';
```

## 3. 配置文件修改

### 3.1 数据库配置
编辑 `ruoyi-admin/src/main/resources/application-druid.yml`：
```yaml
spring:
  datasource:
    druid:
      master:
        url: *************************************************************************************************************************************************
        username: your_username
        password: your_password
```

### 3.2 Redis配置
编辑 `ruoyi-admin/src/main/resources/application.yml`：
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
```

### 3.3 外部系统配置
```yaml
# 灵感广场系统配置
inspiration:
  square:
    base-url: http://your-inspiration-square-host:port
    connect-timeout: 10
    read-timeout: 30

# 外部系统配置
external:
  api:
    auth:
      enabled: true
    signature:
      expire: 300
    log:
      retain:
        days: 30
```

## 4. 编译和打包

### 4.1 清理和编译
```bash
# 进入项目根目录
cd /path/to/RuoYi-Vue-master

# 清理之前的编译结果
mvn clean

# 编译项目
mvn compile

# 打包项目
mvn package -Dmaven.test.skip=true
```

### 4.2 检查打包结果
```bash
# 检查生成的jar包
ls -la ruoyi-admin/target/ruoyi-admin.jar
```

## 5. 系统启动

### 5.1 启动应用
```bash
# 方式1：直接运行jar包
java -jar ruoyi-admin/target/ruoyi-admin.jar

# 方式2：指定配置文件
java -jar ruoyi-admin/target/ruoyi-admin.jar --spring.config.location=classpath:/application.yml

# 方式3：后台运行
nohup java -jar ruoyi-admin/target/ruoyi-admin.jar > logs/application.log 2>&1 &
```

### 5.2 验证启动
```bash
# 检查应用是否启动成功
curl http://localhost:8080/

# 检查外部API是否可访问
curl -H "X-API-Key: test" http://localhost:8080/external/api/inspiration/categories
```

## 6. 系统配置

### 6.1 登录管理后台
- 访问：http://localhost:8080
- 用户名：admin
- 密码：admin123

### 6.2 配置外部系统
1. 进入"系统管理" -> "外部系统管理" -> "系统配置"
2. 点击"新增"按钮
3. 填写系统信息：
   - 系统名称：灵感广场系统
   - 系统编码：INSPIRATION_SQUARE
   - 基础URL：http://inspiration-square-host:port
   - 超时时间：30秒
   - 重试次数：3次
   - IP白名单：***********/24,127.0.0.1
   - 限流配置：1000（每分钟请求数）

### 6.3 获取API密钥
1. 保存系统配置后，系统会自动生成API密钥
2. 点击"查看"按钮，复制API密钥和密钥密文
3. 将密钥信息提供给调用方

### 6.4 测试新增的素材管理接口
部署完成后，可以测试新增的两个素材管理接口。

**重要说明**：这两个接口是**客户端接口**，本机作为客户端调用外部灵感广场系统获取数据。

#### 6.4.1 配置外部灵感广场系统
1. **配置系统地址**：在 `application.yml` 中配置外部灵感广场系统的地址：
```yaml
inspiration:
  square:
    base-url: http://外部灵感广场系统IP:端口  # 例如：http://*************:9001
```

2. **配置API认证**：在管理后台配置外部系统认证信息：
   - 进入"系统管理" -> "外部系统管理" -> "系统配置"
   - 确保已配置"灵感广场系统"（系统编码：INSPIRATION_SQUARE）
   - 系统会自动使用配置的API密钥和密钥密文进行认证

#### 6.4.2 测试个人创作素材接口
```bash
# 先登录获取Token
curl -X POST 'http://localhost:8080/login' \
  -H 'Content-Type: application/json' \
  -d '{"username": "admin", "password": "admin123"}'

# 使用返回的token测试接口（本机调用外部系统）
curl -X GET 'http://localhost:8080/external/api/inspiration/GetAllPersonDetail' \
  -H 'Token: 你的token值'
```

#### 6.4.3 测试灵感广场素材接口
```bash
curl -X GET 'http://localhost:8080/external/api/inspiration/GetAllContentDetail?userId=admin' \
  -H 'Token: 你的token值'
```

#### 6.4.4 预期响应格式
**个人创作素材响应**（从外部系统获取）：
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "type": 1,
            "List": [
                {
                    "title": "我的创作图片1",
                    "photo": "http://*************:2283/profile/materials/thumbnails/image1_thumb.jpg",
                    "photourl": "http://*************:2283/profile/materials/images/image1.jpg",
                    "tag": "人物",
                    "guiId": "personal_img_1704441600000_1"
                }
            ]
        }
    ]
}
```

**灵感广场素材响应**（从外部系统获取）：
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "tag": "人物",
            "tagnum": 2,
            "List": [
                {
                    "title": "经典人物肖像",
                    "photourl": "http://外部系统地址/thumbnails/portrait1.jpg",
                    "detailurl": "http://外部系统地址/images/portrait1_detail.jpg",
                    "guiId": "inspiration_default_1"
                }
            ]
        }
    ]
}
```

**注意**：
1. 如果外部灵感广场系统不可用，接口会自动返回模拟数据作为备选方案
2. 调用外部系统时使用标准的 `Authorization: Bearer <token>` 认证方式
3. 外部系统的API路径必须包含 `/external/api/inspiration/` 前缀

**重要更新**：根据灵感广场系统反馈，已修复以下问题：
- ✅ URL路径：添加了正确的 `/external/api/inspiration/` 前缀
- ✅ 认证方式：简化为标准的Bearer Token认证
- ✅ 参数格式：修复了URL参数格式错误

## 7. 定时任务配置

### 7.1 配置日志清理任务
1. 进入"系统监控" -> "定时任务"
2. 点击"新增"按钮
3. 配置任务信息：
   - 任务名称：外部API日志清理
   - 任务组名：SYSTEM
   - 调用目标字符串：externalApiLogCleanTask.cleanExpiredLogs('30')
   - cron表达式：0 0 2 * * ?（每天凌晨2点执行）
   - 是否并发：否

### 7.2 配置健康检查任务
```
任务名称：外部系统健康检查
调用目标字符串：externalApiLogCleanTask.checkExternalSystemHealth
cron表达式：0 */10 * * * ?（每10分钟执行一次）
```

## 8. 监控和日志

### 8.1 查看应用日志
```bash
# 查看实时日志
tail -f logs/application.log

# 查看错误日志
grep ERROR logs/application.log

# 查看外部API调用日志
grep "外部系统" logs/application.log
```

### 8.2 监控外部API调用
1. 进入"系统管理" -> "外部系统管理" -> "调用日志"
2. 可以查看：
   - API调用记录
   - 成功率统计
   - 响应时间分析
   - 错误信息详情

### 8.3 性能监控
```bash
# 检查JVM内存使用
jstat -gc [pid]

# 检查线程状态
jstack [pid]

# 检查网络连接
netstat -an | grep 8080
```

## 9. 安全配置

### 9.1 防火墙配置
```bash
# 开放应用端口
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload

# 限制外部API访问（可选）
iptables -A INPUT -p tcp --dport 8080 -s ***********/24 -j ACCEPT
```

### 9.2 SSL证书配置（生产环境）
```yaml
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: your_password
    key-store-type: PKCS12
    key-alias: your_alias
```

## 10. 故障排查

### 10.1 常见问题
1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 检查防火墙设置

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证密码配置
   - 检查网络连通性

3. **外部API调用失败**
   - 检查网络连通性
   - 验证API密钥配置
   - 查看详细错误日志

4. **素材管理接口问题**
   - **Token无效错误**: 确保使用有效的JWT Token，检查Token是否过期
   - **404错误**: 确认接口路径正确 `/external/api/inspiration/GetAllPersonDetail`
   - **参数错误**: GetAllContentDetail接口需要userId参数
   - **认证失败**: 检查外部系统配置中的API密钥是否正确
   - **签名错误**: 确保外部系统的API密钥密文配置正确
   - **数据为空**: 检查灵感广场系统连接状态，系统会自动使用模拟数据作为备选

### 10.2 日志分析
```bash
# 查看启动错误
grep "ERROR" logs/application.log | head -20

# 查看外部API错误
grep "外部API" logs/application.log | grep "ERROR"

# 查看数据库连接错误
grep "database" logs/application.log | grep "ERROR"

# 查看素材管理接口相关日志
grep "获取个人创作素材\|获取灵感广场素材" logs/application.log

# 查看Token验证相关错误
grep "Token无效\|Token.*过期" logs/application.log
```

## 11. 备份和恢复

### 11.1 数据备份
```bash
# 备份数据库
mysqldump -u root -p cjviewer > backup/cjviewer_$(date +%Y%m%d).sql

# 备份配置文件
tar -czf backup/config_$(date +%Y%m%d).tar.gz ruoyi-admin/src/main/resources/
```

### 11.2 数据恢复
```bash
# 恢复数据库
mysql -u root -p cjviewer < backup/cjviewer_20250105.sql

# 恢复配置文件
tar -xzf backup/config_20250105.tar.gz
```

## 12. 外部系统配置（灵感广场系统）

### 12.1 灵感广场系统需要提供的API接口

**重要**：灵感广场系统需要提供以下标准REST API接口供本系统调用：

#### 12.1.1 必需接口
```bash
# 1. 获取分类列表
GET /api/categories
Response: {
  "code": 200,
  "data": [
    {
      "categoryId": 1,
      "categoryName": "图片素材",
      "categoryCode": "IMAGE",
      "materialCount": 100
    }
  ]
}

# 2. 获取素材列表
GET /api/materials?categoryId=1&keyword=设计&pageNum=1&pageSize=10
Response: {
  "code": 200,
  "data": [
    {
      "materialId": 1,
      "title": "创意设计",
      "materialType": "IMAGE",
      "categoryId": 1,
      "isFree": true,
      "viewCount": 100
    }
  ]
}

# 3. 获取素材详情
GET /api/materials/{materialId}
Response: {
  "code": 200,
  "data": {
    "materialId": 1,
    "title": "创意设计",
    "description": "素材描述",
    "materialUrl": "http://example.com/material.jpg"
  }
}
```

#### 12.1.2 可选接口
```bash
# 4. 搜索素材
POST /api/materials/search
Request Body: {
  "title": "创意",
  "materialType": "IMAGE",
  "isFree": true
}

# 5. 热门素材
GET /api/materials/hot?limit=10

# 6. 最新素材
GET /api/materials/latest?limit=10

# 7. 记录访问
POST /api/materials/{materialId}/visit
Request Body: {
  "systemCode": "RUOYI_SYSTEM",
  "visitTime": "2025-01-05T10:30:00"
}

# 8. 统计信息
GET /api/statistics?systemCode=RUOYI_SYSTEM
```

### 12.2 灵感广场系统网络配置

#### 12.2.1 防火墙设置
```bash
# 灵感广场系统需要开放API端口供本系统访问
# 假设灵感广场系统运行在9001端口
firewall-cmd --permanent --add-port=9001/tcp
firewall-cmd --reload

# 或者只允许本系统IP访问（推荐）
iptables -A INPUT -p tcp --dport 9001 -s [本系统IP] -j ACCEPT
```

#### 12.2.2 跨域配置（如果需要）
如果灵感广场系统是Web应用，可能需要配置CORS：
```java
// Spring Boot示例
@CrossOrigin(origins = "http://本系统IP:8080")
@RestController
public class MaterialController {
    // API实现
}
```

### 12.3 灵感广场系统数据库要求

#### 12.3.1 建议的数据表结构
```sql
-- 分类表
CREATE TABLE inspiration_category (
    category_id BIGINT PRIMARY KEY,
    category_name VARCHAR(100),
    category_code VARCHAR(50),
    parent_id BIGINT,
    material_count BIGINT DEFAULT 0
);

-- 素材表
CREATE TABLE inspiration_material (
    material_id BIGINT PRIMARY KEY,
    title VARCHAR(200),
    description TEXT,
    material_type VARCHAR(50),
    category_id BIGINT,
    material_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    file_size BIGINT,
    is_free BOOLEAN DEFAULT TRUE,
    view_count BIGINT DEFAULT 0,
    download_count BIGINT DEFAULT 0,
    rating DECIMAL(3,2),
    create_time DATETIME,
    update_time DATETIME
);
```

### 12.4 外部系统（灵感广场）需要实现的验证机制

#### 12.4.1 API认证验证
外部灵感广场系统需要实现以下认证验证逻辑：

**Java示例实现**：
```java
@Component
public class ExternalApiAuthFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 1. 验证API密钥
        String apiKey = httpRequest.getHeader("X-API-Key");
        if (!isValidApiKey(apiKey)) {
            sendErrorResponse(response, 401, "Invalid API Key");
            return;
        }

        // 2. 验证时间戳（防重放攻击）
        String timestamp = httpRequest.getHeader("X-Timestamp");
        if (!isValidTimestamp(timestamp)) {
            sendErrorResponse(response, 401, "Invalid or expired timestamp");
            return;
        }

        // 3. 验证签名
        String signature = httpRequest.getHeader("X-Signature");
        if (!isValidSignature(httpRequest, signature, timestamp)) {
            sendErrorResponse(response, 401, "Invalid signature");
            return;
        }

        // 4. 验证用户Token（可选，根据业务需求）
        String userToken = httpRequest.getHeader("Token");
        if (!isValidUserToken(userToken)) {
            sendErrorResponse(response, 401, "Invalid user token");
            return;
        }

        chain.doFilter(request, response);
    }

    private boolean isValidApiKey(String apiKey) {
        // 验证API密钥是否存在且有效
        return "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6".equals(apiKey);
    }

    private boolean isValidTimestamp(String timestamp) {
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis() / 1000;
            return Math.abs(currentTime - requestTime) <= 300; // 5分钟有效期
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidSignature(HttpServletRequest request, String signature, String timestamp) {
        try {
            // 构建签名字符串
            String method = request.getMethod().toUpperCase();
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();

            StringBuilder signString = new StringBuilder();
            signString.append(method).append(uri);
            if (queryString != null) {
                signString.append("?").append(queryString);
            }
            signString.append(timestamp);

            // 使用HMAC-SHA256生成期望的签名
            String apiSecret = "q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6j7k8l9z0x1c2v3b4n5m6q7w8e9r0t1y2u3i4o5p6";
            String expectedSignature = generateHmacSha256(signString.toString(), apiSecret);

            return signature.equals(expectedSignature);
        } catch (Exception e) {
            return false;
        }
    }

    private String generateHmacSha256(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] signatureBytes = mac.doFinal(data.getBytes());

        StringBuilder hexString = new StringBuilder();
        for (byte b : signatureBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private boolean isValidUserToken(String userToken) {
        // 根据业务需求验证用户Token
        // 可以调用用户服务验证Token有效性
        return userToken != null && userToken.length() > 0;
    }
}
```

#### 12.4.2 接口实现示例
```java
@RestController
public class MaterialController {

    @GetMapping("/GetAllPersonDetail")
    public ResponseEntity<?> getAllPersonDetail(HttpServletRequest request) {
        // 获取用户Token（已通过过滤器验证）
        String userToken = request.getHeader("Token");

        // 根据Token获取用户信息和个人创作素材
        List<Map<String, Object>> data = materialService.getPersonalMaterials(userToken);

        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/GetAllContentDetail")
    public ResponseEntity<?> getAllContentDetail(
            @RequestParam("userId") String userId,
            HttpServletRequest request) {

        String userToken = request.getHeader("Token");

        // 获取灵感广场素材数据
        List<Map<String, Object>> data = materialService.getInspirationMaterials(userToken, userId);

        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "success");
        response.put("data", data);

        return ResponseEntity.ok(response);
    }
}
```

#### 12.4.3 如果灵感广场系统暂时无法提供API

**临时解决方案**：
1. **数据库直连方案**（不推荐生产环境）
2. **文件同步方案**
3. **消息队列方案**

详细实现请参考技术文档。

---

## 本系统性能优化

## 13. 性能优化

### 13.1 JVM参数优化
```bash
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 13.2 数据库优化
```sql
-- 添加索引优化查询性能
ALTER TABLE external_api_log ADD INDEX idx_request_time_system (request_time, system_id);
ALTER TABLE external_api_log ADD INDEX idx_success_time (is_success, request_time);

-- 定期清理过期数据
DELETE FROM external_api_log WHERE request_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 13.3 Redis优化
```bash
# 配置Redis内存策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru
redis-cli CONFIG SET maxmemory 1gb
```

---

## 总结

### 本系统（RuoYi）需要做的：
✅ 第1-11章的所有部署步骤

### 外部系统（灵感广场）需要做的：
🔧 提供第12章中的API接口
🔧 配置网络访问权限
🔧 确保数据库结构符合要求

### 如果外部系统暂时无法配合：
💡 可以使用第12.4章的临时解决方案
💡 或者先使用模拟数据进行测试

## 15. 新增功能说明

### 15.1 素材管理接口
本次部署新增了两个重要的素材管理接口：

1. **个人创作素材接口** (`/external/api/inspiration/GetAllPersonDetail`)
   - 获取用户个人创作的所有素材
   - 按类型分组（1=图片，2=3D模型）
   - 包含完整的素材信息和URL

2. **灵感广场素材接口** (`/external/api/inspiration/GetAllContentDetail`)
   - 获取灵感广场的所有素材
   - 按标签分组，包含素材数量统计
   - 支持远程数据和本地模拟数据

### 15.2 接口特性
- ✅ **JWT Token认证**: 使用标准的Token认证机制
- ✅ **完整URL返回**: 自动构建可直接访问的完整URL
- ✅ **容错处理**: 远程系统不可用时自动使用模拟数据
- ✅ **数据分组**: 智能按类型或标签分组
- ✅ **统一管理**: 集成在现有的灵感广场控制器中

### 15.3 使用建议
1. **开发环境**: 可以直接使用模拟数据进行测试
2. **生产环境**: 需要配置真实的灵感广场系统连接
3. **性能优化**: 建议添加Redis缓存来提高响应速度
4. **数据扩展**: 可以根据实际需求扩展素材字段和分类

---

部署完成后，系统将提供安全、高效的灵感广场数据接口服务，包括完整的素材管理功能！
