# 用户个人资料管理API文档

## 概述

新增了用户个人资料管理功能，支持修改当前用户的昵称、电子邮件、出生日期和个人简介。

## 数据库变更

### 新增字段

在 `sys_user` 表中新增了以下字段：

```sql
-- 出生日期
birth_date DATE DEFAULT NULL COMMENT '出生日期'

-- 个人简介  
bio TEXT DEFAULT NULL COMMENT '个人简介'
```

### 执行脚本

请执行以下SQL脚本添加新字段：

```sql
-- 执行 sql/user_profile_extension.sql 文件
ALTER TABLE sys_user 
ADD COLUMN birth_date DATE DEFAULT NULL COMMENT '出生日期',
ADD COLUMN bio TEXT DEFAULT NULL COMMENT '个人简介';
```

## API接口

### 1. 修改个人资料

**接口地址：** `GET /system/user/profile/updateProfile`

**接口描述：** 修改当前用户的个人资料信息

**请求参数：** (URL参数)

```
GET /system/user/profile/updateProfile?nickName=新昵称&email=<EMAIL>&birthDate=1990-01-01&bio=这是我的个人简介
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 | 限制 |
|--------|------|------|------|------|
| nickName | String | 否 | 用户昵称 | 最大30个字符 |
| email | String | 否 | 用户邮箱 | 邮箱格式，最大50个字符 |
| birthDate | String | 否 | 出生日期 | yyyy-MM-dd格式 |
| bio | String | 否 | 个人简介 | 最大500个字符 |

**响应示例：**

```json
{
  "code": 200,
  "msg": "个人资料修改成功",
  "data": null
}
```

### 2. 获取个人资料详情

**接口地址：** `GET /system/user/profile/detail`

**响应示例：**

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "/profile/avatar/2024/01/01/avatar.jpg",
    "birthDate": "1990-01-01",
    "bio": "系统管理员，负责系统维护和用户管理",
    "createTime": "2024-01-01 10:00:00",
    "loginDate": "2024-08-14 18:00:00",
    "roleGroup": "超级管理员",
    "postGroup": "董事长"
  }
}
```

## 前端调用示例

### JavaScript/Vue示例

```javascript
// 修改个人资料
async function updateUserProfile(profileData) {
  try {
    // 构建URL参数
    const params = new URLSearchParams();
    if (profileData.nickName) params.append('nickName', profileData.nickName);
    if (profileData.email) params.append('email', profileData.email);
    if (profileData.birthDate) params.append('birthDate', profileData.birthDate);
    if (profileData.bio) params.append('bio', profileData.bio);

    const response = await this.$http.get(`/system/user/profile/updateProfile?${params.toString()}`);

    if (response.code === 200) {
      this.$message.success('个人资料修改成功');
    } else {
      this.$message.error(response.msg);
    }
  } catch (error) {
    this.$message.error('修改失败：' + error.message);
  }
}

// 获取个人资料详情
async function getUserProfileDetail() {
  try {
    const response = await this.$http.get('/system/user/profile/detail');
    if (response.code === 200) {
      this.userProfile = response.data;
    }
  } catch (error) {
    this.$message.error('获取用户信息失败');
  }
}
```

### 表单验证示例

```javascript
// 表单验证规则
const profileRules = {
  nickName: [
    { max: 30, message: '用户昵称长度不能超过30个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    { max: 50, message: '邮箱长度不能超过50个字符', trigger: 'blur' }
  ],
  bio: [
    { max: 500, message: '个人简介长度不能超过500个字符', trigger: 'blur' }
  ]
};
```

## 注意事项

1. **权限控制**：接口会自动验证用户登录状态，只能修改当前登录用户的信息
2. **邮箱唯一性**：系统会检查邮箱是否已被其他用户使用
3. **数据验证**：所有输入数据都会进行格式和长度验证
4. **XSS防护**：昵称和个人简介字段具有XSS攻击防护
5. **缓存更新**：修改成功后会自动更新用户缓存信息
6. **日志记录**：所有修改操作都会记录到系统日志中

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 参数验证失败 |
| 500 | 服务器内部错误 |
| 401 | 未登录或登录已过期 |
