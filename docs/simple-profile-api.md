# 简化用户信息接口文档

## 接口说明
新增了一个简化的用户信息接口，只返回必要的用户字段，减少数据传输量。

## 接口详情

### 请求信息
- **接口地址**: `/system/user/profile/simple`
- **请求方式**: `GET`
- **接口描述**: 获取简化的个人信息
- **需要认证**: 是（需要登录）

### 请求头
```
Authorization: Bearer {token}
Content-Type: application/json
```

### 响应示例

#### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "若依",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "http://localhost:8080/profile/avatar/2025/08/05/fbdd10cae1d44a2fb2ec7a67f0ff56f1.jpg",
    "status": "0",
    "delFlag": "0",
    "birthDate": "1990-01-01",
    "bio": "这是我的个人简介"
  }
}
```

#### 失败响应
```json
{
  "code": 401,
  "msg": "认证失败"
}
```

## 字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| userId | Long | 用户ID | 1 |
| userName | String | 用户账号 | "admin" |
| nickName | String | 用户昵称 | "若依" |
| email | String | 用户邮箱 | "<EMAIL>" |
| phonenumber | String | 手机号码 | "15888888888" |
| sex | String | 用户性别 | "1"（1男 0女 2未知） |
| avatar | String | 头像完整URL | "http://localhost:8080/profile/avatar/xxx.jpg" |
| status | String | 帐号状态 | "0"（0正常 1停用） |
| delFlag | String | 删除标志 | "0"（0存在 2删除） |
| birthDate | Date | 出生日期 | "1990-01-01" |
| bio | String | 个人简介 | "这是我的个人简介" |

## 与原接口的区别

### 原接口 `/system/user/profile`
- 返回完整的用户信息
- 包含角色组和岗位组信息
- 数据量较大

### 新接口 `/system/user/profile/simple`
- 只返回11个核心字段（新增出生日期和个人简介）
- 不包含角色和岗位信息
- 数据量小，响应速度快
- 适合移动端或需要频繁调用的场景

## 使用场景

1. **移动端应用**: 减少流量消耗
2. **频繁调用**: 如实时显示用户信息
3. **简单展示**: 只需要基本用户信息的场景
4. **性能优化**: 减少不必要的数据传输

## 调用示例

### JavaScript (Axios)
```javascript
// 获取简化用户信息
axios.get('/system/user/profile/simple', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => {
  const userInfo = response.data.data;
  console.log('用户信息:', userInfo);
  // 使用用户信息
  document.getElementById('userName').textContent = userInfo.userName;
  document.getElementById('nickName').textContent = userInfo.nickName;
})
.catch(error => {
  console.error('获取用户信息失败:', error);
});
```

### Java (RestTemplate)
```java
// 设置请求头
HttpHeaders headers = new HttpHeaders();
headers.set("Authorization", "Bearer " + token);
HttpEntity<String> entity = new HttpEntity<>(headers);

// 发送请求
ResponseEntity<AjaxResult> response = restTemplate.exchange(
    "/system/user/profile/simple",
    HttpMethod.GET,
    entity,
    AjaxResult.class
);

// 处理响应
if (response.getBody().getCode() == 200) {
    SimpleUserProfileDto userInfo = (SimpleUserProfileDto) response.getBody().getData();
    System.out.println("用户信息: " + userInfo);
}
```

### cURL
```bash
curl -X GET \
  'http://localhost:8080/system/user/profile/simple' \
  -H 'Authorization: Bearer your_token_here' \
  -H 'Content-Type: application/json'
```

## 注意事项

1. **认证要求**: 必须提供有效的JWT Token
2. **权限控制**: 只能获取当前登录用户的信息
3. **头像URL**: 自动转换为完整的URL地址，可以直接使用
4. **缓存建议**: 可以在前端适当缓存，减少请求频率
5. **错误处理**: 需要处理401认证失败的情况

## 头像URL处理说明

接口会自动处理头像URL：
- **相对路径**: 如 `/profile/avatar/xxx.jpg` 会自动转换为完整URL
- **完整URL**: 如 `http://example.com/avatar.jpg` 保持不变
- **空值处理**: 如果用户没有设置头像，返回空字符串或null

示例转换：
```
数据库存储: /profile/avatar/2025/08/05/fbdd10cae1d44a2fb2ec7a67f0ff56f1.jpg
接口返回: http://localhost:8080/profile/avatar/2025/08/05/fbdd10cae1d44a2fb2ec7a67f0ff56f1.jpg
```

## 版本信息
- 新增时间: 2025-01-05
- 接口版本: v1.0
- 兼容性: 与原有接口完全兼容，不影响现有功能
