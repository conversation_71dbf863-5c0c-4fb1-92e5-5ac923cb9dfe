# 个人资料修改接口 - 快速测试示例

## 🚀 快速测试（GET方式）

### 1. 获取Token
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 修改个人资料（超简单）

#### 只修改昵称
```bash
curl "http://localhost:8080/system/user/profile/updateProfile?nickName=新昵称" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 只修改邮箱
```bash
curl "http://localhost:8080/system/user/profile/updateProfile?email=<EMAIL>" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 只修改出生日期
```bash
curl "http://localhost:8080/system/user/profile/updateProfile?birthDate=1990-01-01" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 只修改个人简介
```bash
curl "http://localhost:8080/system/user/profile/updateProfile?bio=这是我的新简介" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 修改多个字段
```bash
curl "http://localhost:8080/system/user/profile/updateProfile?nickName=测试用户&email=<EMAIL>&birthDate=1990-05-15&bio=完整的个人简介" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 查看修改结果
```bash
curl "http://localhost:8080/system/user/profile/detail" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🌐 浏览器直接测试

### 步骤1：登录获取token
在浏览器中访问：`http://localhost:8080/login`

### 步骤2：在浏览器地址栏直接测试
```
http://localhost:8080/system/user/profile/updateProfile?nickName=浏览器测试&bio=通过浏览器修改的简介
```

**注意：** 需要在请求头中添加Authorization，建议使用浏览器插件或开发者工具。

## 📱 前端JavaScript示例

### 简单版本
```javascript
// 修改昵称
fetch('/system/user/profile/updateProfile?nickName=新昵称', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### 完整版本
```javascript
function updateProfile(data) {
  const params = new URLSearchParams();
  if (data.nickName) params.append('nickName', data.nickName);
  if (data.email) params.append('email', data.email);
  if (data.birthDate) params.append('birthDate', data.birthDate);
  if (data.bio) params.append('bio', data.bio);
  
  fetch(`/system/user/profile/updateProfile?${params.toString()}`, {
    headers: {
      'Authorization': 'Bearer ' + localStorage.getItem('token')
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      alert('修改成功！');
    } else {
      alert('修改失败：' + data.msg);
    }
  });
}

// 使用示例
updateProfile({
  nickName: '新昵称',
  email: '<EMAIL>',
  birthDate: '1990-01-01',
  bio: '这是我的个人简介'
});
```

## 🔧 Postman测试

### 1. 设置环境变量
- `baseUrl`: `http://localhost:8080`
- `token`: `your-jwt-token`

### 2. 创建GET请求
- **URL**: `{{baseUrl}}/system/user/profile/updateProfile`
- **Method**: `GET`
- **Headers**: 
  - `Authorization`: `Bearer {{token}}`
- **Params**:
  - `nickName`: `测试昵称`
  - `email`: `<EMAIL>`
  - `birthDate`: `1990-01-01`
  - `bio`: `个人简介内容`

## ✅ 验证结果

### 成功响应
```json
{
  "code": 200,
  "msg": "个人资料修改成功",
  "data": null
}
```

### 错误响应示例
```json
{
  "code": 500,
  "msg": "邮箱格式不正确",
  "data": null
}
```

## 🎯 测试要点

1. **参数可选**：所有参数都是可选的，可以只传需要修改的字段
2. **中文支持**：昵称和简介支持中文字符
3. **URL编码**：如果参数包含特殊字符，需要进行URL编码
4. **长度限制**：
   - 昵称：最大30字符
   - 邮箱：最大50字符
   - 简介：最大500字符
5. **格式验证**：
   - 邮箱：必须符合邮箱格式
   - 日期：必须是yyyy-MM-dd格式

## 🚨 常见错误

1. **401 Unauthorized**：token无效或过期
2. **邮箱格式不正确**：邮箱不符合格式要求
3. **邮箱已存在**：邮箱被其他用户使用
4. **字段长度超限**：输入内容超过最大长度限制
5. **日期格式错误**：日期不是yyyy-MM-dd格式

现在您可以直接在浏览器地址栏或使用简单的curl命令来测试接口了！
