# 素材上传接口规范文档

## 接口概述

本文档定义了本系统上传图片文件到素材广场的接口规范，包括本系统的调用接口和素材广场系统需要实现的接收接口。

---

## 1. 本系统提供的上传接口

### 1.1 接口信息
- **接口地址**: `POST /inspiration/upload`
- **功能描述**: 上传图片文件到素材广场系统
- **权限要求**: `system:inspiration:upload`

### 1.2 请求参数

#### 请求头
```
Token: eyJhbGciOiJIUzUxMiJ9...  (必填，用户登录Token)
Content-Type: multipart/form-data
```

#### 请求体（multipart/form-data）
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| file | File | 是 | 上传的图片文件 | image.jpg |
| title | String | 否 | 素材标题 | "美丽风景" |
| description | String | 否 | 素材描述 | "这是一张美丽的风景照片" |
| category | String | 否 | 素材分类 | "风景" |
| tags | String | 否 | 素材标签（多个用逗号分隔） | "风景,自然,山水" |

#### 文件限制
- **文件类型**: 只支持图片文件（image/*）
- **文件大小**: 最大10MB
- **支持格式**: JPG, PNG, GIF, BMP, WEBP等

### 1.3 响应格式

#### 成功响应
```json
{
    "code": 200,
    "msg": "文件上传成功",
    "data": {
        "materialId": "mat_20250113_001",
        "fileName": "landscape.jpg",
        "fileSize": 2048576,
        "uploadTime": "2025-01-13T10:30:00",
        "materialUrl": "http://*************:2283/materials/images/mat_20250113_001.jpg",
        "thumbnailUrl": "http://*************:2283/materials/thumbnails/mat_20250113_001_thumb.jpg"
    }
}
```

#### 失败响应
```json
{
    "code": 500,
    "msg": "上传失败: 文件大小不能超过10MB"
}
```

### 1.4 调用示例

#### JavaScript (FormData)
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('title', '美丽风景');
formData.append('description', '这是一张美丽的风景照片');
formData.append('category', '风景');
formData.append('tags', '风景,自然,山水');

fetch('/inspiration/upload', {
    method: 'POST',
    headers: {
        'Token': localStorage.getItem('token')
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('上传成功:', data.data);
    } else {
        console.error('上传失败:', data.msg);
    }
});
```

#### cURL
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: eyJhbGciOiJIUzUxMiJ9..." \
  -F "file=@/path/to/image.jpg" \
  -F "title=美丽风景" \
  -F "description=这是一张美丽的风景照片" \
  -F "category=风景" \
  -F "tags=风景,自然,山水"
```

---

## 2. 素材广场系统需要实现的接收接口

### 2.1 接口信息
- **接口地址**: `POST /external/api/inspiration/upload`
- **功能描述**: 接收并处理上传的素材文件
- **认证方式**: `Authorization: Bearer <token>`

### 2.2 请求参数

#### 请求头
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...  (必填)
Content-Type: multipart/form-data
```

#### 请求体（multipart/form-data）
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| file | File | 是 | 上传的图片文件 | image.jpg |
| title | String | 否 | 素材标题 | "美丽风景" |
| description | String | 否 | 素材描述 | "这是一张美丽的风景照片" |
| category | String | 否 | 素材分类 | "风景" |
| tags | String | 否 | 素材标签（多个用逗号分隔） | "风景,自然,山水" |

### 2.3 处理要求

#### 文件处理
1. **文件验证**：
   - 验证文件类型（只接受图片）
   - 验证文件大小（建议限制10MB以内）
   - 验证文件完整性

2. **文件存储**：
   - 生成唯一的文件名（避免重名冲突）
   - 存储原始文件
   - 生成缩略图（建议尺寸：200x200）

3. **数据库记录**：
   - 保存素材信息到数据库
   - 记录上传用户、时间等信息
   - 生成唯一的素材ID

#### 响应要求
```json
{
    "code": 200,
    "msg": "success",
    "materialId": "mat_20250113_001",
    "materialUrl": "http://*************:2283/materials/images/mat_20250113_001.jpg",
    "thumbnailUrl": "http://*************:2283/materials/thumbnails/mat_20250113_001_thumb.jpg"
}
```

### 2.4 Java实现示例

```java
@RestController
@RequestMapping("/external/api/inspiration")
public class MaterialUploadController {

    @PostMapping("/upload")
    public ResponseEntity<?> uploadMaterial(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "tags", required = false) String tags,
            HttpServletRequest request) {
        
        try {
            // 1. 验证认证
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.status(401).body(Map.of("code", 401, "msg", "认证失败"));
            }
            
            String token = authHeader.substring(7);
            // 验证token有效性...
            
            // 2. 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "文件不能为空"));
            }
            
            if (!file.getContentType().startsWith("image/")) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "只支持图片文件"));
            }
            
            // 3. 处理文件上传
            String materialId = generateMaterialId();
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String newFilename = materialId + fileExtension;
            
            // 保存原始文件
            String materialPath = "/materials/images/" + newFilename;
            file.transferTo(new File(uploadDir + materialPath));
            
            // 生成缩略图
            String thumbnailPath = "/materials/thumbnails/" + materialId + "_thumb" + fileExtension;
            generateThumbnail(uploadDir + materialPath, uploadDir + thumbnailPath);
            
            // 4. 保存到数据库
            Material material = new Material();
            material.setMaterialId(materialId);
            material.setTitle(title);
            material.setDescription(description);
            material.setCategory(category);
            material.setTags(tags);
            material.setMaterialUrl(baseUrl + materialPath);
            material.setThumbnailUrl(baseUrl + thumbnailPath);
            material.setUploadTime(new Date());
            materialService.save(material);
            
            // 5. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("msg", "success");
            result.put("materialId", materialId);
            result.put("materialUrl", baseUrl + materialPath);
            result.put("thumbnailUrl", baseUrl + thumbnailPath);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("code", 500, "msg", "上传失败: " + e.getMessage()));
        }
    }
    
    private String generateMaterialId() {
        return "mat_" + System.currentTimeMillis();
    }
    
    private void generateThumbnail(String sourcePath, String thumbnailPath) {
        // 使用图片处理库生成缩略图
        // 例如使用 Thumbnailator 库
    }
}
```

---

## 3. 错误处理

### 3.1 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查文件和参数格式 |
| 401 | 认证失败 | 检查Token是否有效 |
| 413 | 文件过大 | 减小文件大小 |
| 415 | 不支持的文件类型 | 使用支持的图片格式 |
| 500 | 服务器内部错误 | 检查服务器日志 |

### 3.2 客户端错误处理
```javascript
fetch('/inspiration/upload', {
    method: 'POST',
    headers: { 'Token': token },
    body: formData
})
.then(response => {
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
})
.then(data => {
    if (data.code === 200) {
        console.log('上传成功');
    } else {
        throw new Error(data.msg);
    }
})
.catch(error => {
    console.error('上传失败:', error.message);
    // 显示错误提示给用户
});
```

---

## 4. 安全考虑

1. **文件类型验证**：严格验证文件MIME类型
2. **文件大小限制**：防止大文件攻击
3. **文件名安全**：防止路径遍历攻击
4. **病毒扫描**：对上传文件进行安全扫描
5. **访问控制**：验证用户权限
6. **存储隔离**：不同用户的文件分开存储

接口实现完成后，两个系统就可以实现文件上传的完整流程！
