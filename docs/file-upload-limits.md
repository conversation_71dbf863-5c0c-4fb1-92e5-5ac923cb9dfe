# 文件上传大小限制配置

## 问题描述

上传文件时遇到错误：
```json
{
    "msg": "Maximum upload size exceeded; nested exception is java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 10485760 bytes.",
    "code": 500
}
```

## 问题原因

Spring Boot默认的文件上传大小限制为1MB，这对于图片和3D模型文件来说太小了。

## 解决方案

### ✅ 1. 修改application.yml配置

```yaml
spring:
  servlet:
    multipart:
      # 单个文件大小：50MB（支持大图片和模型文件）
      max-file-size: 50MB
      # 总上传数据大小：100MB（支持同时上传预览图和模型文件）
      max-request-size: 100MB
```

### ✅ 2. 添加Java配置类

创建 `MultipartConfig.java` 确保配置生效：
```java
@Configuration
public class MultipartConfig {
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        factory.setMaxRequestSize(DataSize.ofMegabytes(100));
        return factory.createMultipartConfig();
    }
}
```

### ✅ 3. 更新业务层验证

```java
// 图片上传：最大50MB
if (file.getSize() > 50 * 1024 * 1024) {
    throw new RuntimeException("文件大小不能超过50MB");
}

// 模型文件：最大50MB
if (modelFile.getSize() > 50 * 1024 * 1024) {
    throw new RuntimeException("模型文件大小不能超过50MB");
}
```

## 当前文件大小限制

### 图片上传接口 (`/inspiration/upload`)
- **单个图片文件**: 最大50MB
- **支持格式**: JPG, PNG, GIF, BMP, WEBP等
- **推荐大小**: 建议10MB以内，确保上传速度

### 模型上传接口 (`/inspiration/uploadModel`)
- **预览图片**: 最大50MB
- **模型文件**: 最大50MB
- **总请求大小**: 最大100MB
- **支持格式**: 
  - 图片：JPG, PNG, GIF等
  - 模型：.glb, .gltf, .obj, .fbx, .3ds, .dae, .ply

## 测试验证

### 1. 重启应用
修改配置后需要重启Spring Boot应用：
```bash
# 停止应用
# 重新启动应用
```

### 2. 测试小文件（应该成功）
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@small_image.jpg" \
  -F "title=测试小图片"
```

### 3. 测试大文件（现在应该成功）
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@large_image.jpg" \
  -F "title=测试大图片"
```

### 4. 测试模型上传
```bash
curl -X POST "http://localhost:8080/inspiration/uploadModel" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@preview.jpg" \
  -F "modelFile=@model.glb" \
  -F "title=测试模型"
```

## 错误处理

### 如果仍然出现大小限制错误

1. **检查Tomcat配置**（如果使用外部Tomcat）：
```xml
<!-- server.xml -->
<Connector port="8080" protocol="HTTP/1.1"
           maxPostSize="104857600"
           maxSwallowSize="104857600" />
```

2. **检查Nginx配置**（如果使用Nginx代理）：
```nginx
# nginx.conf
client_max_body_size 100M;
```

3. **检查应用服务器内存**：
确保服务器有足够内存处理大文件上传。

### 常见错误和解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `FileSizeLimitExceededException` | 单个文件超过限制 | 增加 `max-file-size` |
| `SizeLimitExceededException` | 总请求大小超过限制 | 增加 `max-request-size` |
| `OutOfMemoryError` | 服务器内存不足 | 增加JVM内存或优化文件处理 |

## 性能优化建议

### 1. 前端优化
```javascript
// 文件大小检查
function validateFileSize(file, maxSizeMB) {
    const maxSize = maxSizeMB * 1024 * 1024;
    if (file.size > maxSize) {
        alert(`文件大小不能超过${maxSizeMB}MB`);
        return false;
    }
    return true;
}

// 图片压缩（可选）
function compressImage(file, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            canvas.toBlob(resolve, 'image/jpeg', quality);
        };
        
        img.src = URL.createObjectURL(file);
    });
}
```

### 2. 后端优化
```java
// 流式处理大文件（避免内存溢出）
@Value("${file.upload.temp-dir:/tmp}")
private String tempDir;

// 使用临时文件处理大文件上传
```

### 3. 用户体验优化
- 显示上传进度条
- 提供文件大小预检查
- 支持断点续传（对于超大文件）

## 监控和日志

### 添加上传监控
```java
@Component
public class FileUploadMonitor {
    
    @EventListener
    public void handleFileUpload(FileUploadEvent event) {
        log.info("文件上传: 文件名={}, 大小={}, 用户={}", 
                event.getFileName(), 
                event.getFileSize(), 
                event.getUserId());
    }
}
```

修复完成后，系统应该能够支持最大50MB的单个文件上传，总请求大小最大100MB！
