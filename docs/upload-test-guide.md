# 上传接口测试指南

## 问题分析

根据错误日志，问题是"Token验证失败"，已修复以下问题：

### ✅ 已修复的问题

1. **Token获取逻辑**：
   ```java
   // 修复前：硬编码token
   String token = "system_token";
   
   // 修复后：从请求头获取
   String token = request.getHeader("Token");
   if (StringUtils.isEmpty(token)) {
       String authHeader = request.getHeader("Authorization");
       if (StringUtils.isNotEmpty(authHeader) && authHeader.startsWith("Bearer ")) {
           token = authHeader.substring(7);
       }
   }
   ```

2. **方法参数修复**：
   ```java
   // 修复前：错误的参数
   uploadMaterial(file, title, type, tag, request)
   
   // 修复后：正确的参数
   uploadMaterial(file, title, description, tag, request)
   ```

3. **请求体参数修复**：
   ```java
   // 修复前：
   body.add("type", type);
   
   // 修复后：
   body.add("description", description);
   ```

## 测试步骤

### 1. 获取有效Token

#### 方法1：通过登录接口获取
```bash
curl -X POST 'http://localhost:8080/login' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

响应示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1Njc4LTEyMzQtMTIzNC0xMjM0LTEyMzQ1Njc4OTAxMiJ9.abcdefg..."
}
```

#### 方法2：从浏览器开发者工具获取
1. 登录管理后台
2. 打开浏览器开发者工具 → Network
3. 随便点击一个菜单
4. 查看请求头中的 `Token` 值

### 2. 测试上传接口

#### 使用cURL测试
```bash
# 替换 YOUR_TOKEN 为实际的token值
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@/path/to/your/image.jpg" \
  -F "title=测试图片" \
  -F "description=这是一张测试图片" \
  -F "tag=测试"
```

#### 使用Postman测试
1. **请求方式**：POST
2. **URL**：`http://localhost:8080/inspiration/upload`
3. **Headers**：
   ```
   Token: YOUR_TOKEN
   ```
4. **Body**：选择 `form-data`
   ```
   file: [选择图片文件]
   title: 测试图片
   description: 这是一张测试图片
   tag: 测试
   ```

### 3. 预期响应

#### 成功响应
```json
{
    "code": 200,
    "msg": "文件上传成功",
    "data": {
        "materialId": "mat_1704441600000",
        "fileName": "test.jpg",
        "fileSize": 2048576,
        "uploadTime": "2025-01-13T14:30:00",
        "materialUrl": "http://*************:2283/materials/images/mat_1704441600000.jpg",
        "thumbnailUrl": "http://*************:2283/materials/thumbnails/mat_1704441600000_thumb.jpg"
    }
}
```

#### 失败响应示例
```json
// Token无效
{
    "code": 500,
    "msg": "上传失败: Token验证失败"
}

// 文件类型错误
{
    "code": 500,
    "msg": "上传失败: 只支持图片文件上传"
}

// 文件过大
{
    "code": 500,
    "msg": "上传失败: 文件大小不能超过10MB"
}
```

## 故障排查

### 1. Token相关问题

#### 问题：Token验证失败
**解决方案**：
1. 确认Token格式正确（JWT格式）
2. 确认Token未过期
3. 确认请求头名称正确（`Token` 或 `Authorization: Bearer xxx`）

#### 问题：Token为空
**解决方案**：
1. 检查前端是否正确传递Token
2. 检查请求头名称是否正确

### 2. 文件上传问题

#### 问题：文件类型不支持
**解决方案**：
- 只支持图片文件（image/*）
- 支持的格式：JPG, PNG, GIF, BMP, WEBP

#### 问题：文件过大
**解决方案**：
- 文件大小限制：10MB
- 压缩图片后重试

### 3. 网络连接问题

#### 问题：连接外部系统失败
**解决方案**：
1. 检查外部系统是否可访问：
   ```bash
   curl -v http://*************:2283/external/api/inspiration/upload
   ```
2. 检查网络连通性
3. 检查防火墙设置

## JavaScript测试代码

```html
<!DOCTYPE html>
<html>
<head>
    <title>上传测试</title>
</head>
<body>
    <input type="file" id="fileInput" accept="image/*">
    <input type="text" id="title" placeholder="标题">
    <input type="text" id="description" placeholder="描述">
    <input type="text" id="tag" placeholder="标签">
    <button onclick="uploadFile()">上传</button>
    
    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const title = document.getElementById('title').value;
            const description = document.getElementById('description').value;
            const tag = document.getElementById('tag').value;
            
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('title', title);
            formData.append('description', description);
            formData.append('tag', tag);
            
            // 从localStorage获取token，或者手动输入
            const token = localStorage.getItem('token') || prompt('请输入Token:');
            
            try {
                const response = await fetch('/inspiration/upload', {
                    method: 'POST',
                    headers: {
                        'Token': token
                    },
                    body: formData
                });
                
                const result = await response.json();
                console.log('上传结果:', result);
                
                if (result.code === 200) {
                    alert('上传成功！素材ID: ' + result.data.materialId);
                } else {
                    alert('上传失败: ' + result.msg);
                }
            } catch (error) {
                console.error('上传错误:', error);
                alert('上传失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
```

## 日志监控

上传过程中可以查看以下日志：

1. **应用日志**：
   ```bash
   tail -f logs/application.log | grep -i upload
   ```

2. **外部API调用日志**：
   - 在管理后台查看"外部系统管理" → "调用日志"
   - 查看对灵感广场系统的调用记录

3. **错误日志**：
   ```bash
   tail -f logs/application.log | grep -i error
   ```

修复完成后，上传功能应该可以正常工作了！
