<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>素材管理接口演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .token-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        .material-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .material-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .material-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .material-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .tag {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .type-badge {
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            border: 1px solid #fecaca;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>素材管理接口演示</h1>
            <p>演示获取个人创作素材和灵感广场素材的接口调用</p>
        </div>

        <!-- Token输入区域 -->
        <div class="section">
            <h3>1. 设置Token</h3>
            <input type="text" id="tokenInput" class="token-input" placeholder="请输入登录后获取的Token">
            <button class="btn" onclick="setToken()">设置Token</button>
            <button class="btn" onclick="testLogin()">测试登录</button>
            <div id="tokenStatus"></div>
        </div>

        <!-- 个人创作素材 -->
        <div class="section">
            <h3>2. 我的创作素材</h3>
            <button class="btn" onclick="getPersonalMaterials()" id="personalBtn">获取我的创作素材</button>
            <div id="personalResult" class="result" style="display:none;"></div>
            <div id="personalMaterials" class="material-grid"></div>
        </div>

        <!-- 灵感广场素材 -->
        <div class="section">
            <h3>3. 灵感广场素材</h3>
            <input type="text" id="userIdInput" placeholder="用户ID (如: admin)" value="admin" style="width: 200px; padding: 8px; margin-right: 10px;">
            <button class="btn" onclick="getInspirationMaterials()" id="inspirationBtn">获取灵感广场素材</button>
            <div id="inspirationResult" class="result" style="display:none;"></div>
            <div id="inspirationMaterials" class="material-grid"></div>
        </div>
    </div>

    <script>
        let currentToken = '';

        // 设置Token
        function setToken() {
            const token = document.getElementById('tokenInput').value.trim();
            if (token) {
                currentToken = token;
                localStorage.setItem('demoToken', token);
                showStatus('tokenStatus', 'Token已设置', 'success');
            } else {
                showStatus('tokenStatus', '请输入有效的Token', 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    currentToken = result.token;
                    document.getElementById('tokenInput').value = result.token;
                    localStorage.setItem('demoToken', result.token);
                    showStatus('tokenStatus', '登录成功，Token已自动设置', 'success');
                } else {
                    showStatus('tokenStatus', '登录失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showStatus('tokenStatus', '登录请求失败: ' + error.message, 'error');
            }
        }

        // 获取个人创作素材
        async function getPersonalMaterials() {
            if (!currentToken) {
                showStatus('personalResult', '请先设置Token', 'error');
                return;
            }

            const btn = document.getElementById('personalBtn');
            btn.disabled = true;
            btn.textContent = '获取中...';

            try {
                const response = await fetch('/GetAllPersonDetail', {
                    method: 'GET',
                    headers: {
                        'Token': currentToken
                    }
                });

                const result = await response.json();
                
                document.getElementById('personalResult').style.display = 'block';
                document.getElementById('personalResult').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';

                if (result.code === 200) {
                    displayPersonalMaterials(result.data);
                } else {
                    showStatus('personalMaterials', '获取失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showStatus('personalResult', '请求失败: ' + error.message, 'error');
                document.getElementById('personalResult').style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.textContent = '获取我的创作素材';
            }
        }

        // 获取灵感广场素材
        async function getInspirationMaterials() {
            if (!currentToken) {
                showStatus('inspirationResult', '请先设置Token', 'error');
                return;
            }

            const userId = document.getElementById('userIdInput').value.trim();
            if (!userId) {
                showStatus('inspirationResult', '请输入用户ID', 'error');
                return;
            }

            const btn = document.getElementById('inspirationBtn');
            btn.disabled = true;
            btn.textContent = '获取中...';

            try {
                const response = await fetch(`/GetAllContentDetail?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Token': currentToken
                    }
                });

                const result = await response.json();
                
                document.getElementById('inspirationResult').style.display = 'block';
                document.getElementById('inspirationResult').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';

                if (result.code === 200) {
                    displayInspirationMaterials(result.data);
                } else {
                    showStatus('inspirationMaterials', '获取失败: ' + result.msg, 'error');
                }
            } catch (error) {
                showStatus('inspirationResult', '请求失败: ' + error.message, 'error');
                document.getElementById('inspirationResult').style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.textContent = '获取灵感广场素材';
            }
        }

        // 显示个人创作素材
        function displayPersonalMaterials(data) {
            const container = document.getElementById('personalMaterials');
            container.innerHTML = '';

            data.forEach(typeGroup => {
                const typeTitle = document.createElement('h4');
                typeTitle.textContent = `类型 ${typeGroup.type} (${typeGroup.type === 1 ? '图片' : '3D模型'})`;
                typeTitle.style.gridColumn = '1 / -1';
                container.appendChild(typeTitle);

                typeGroup.List.forEach(material => {
                    const card = createMaterialCard(material, typeGroup.type);
                    container.appendChild(card);
                });
            });
        }

        // 显示灵感广场素材
        function displayInspirationMaterials(data) {
            const container = document.getElementById('inspirationMaterials');
            container.innerHTML = '';

            data.forEach(tagGroup => {
                const tagTitle = document.createElement('h4');
                tagTitle.textContent = `${tagGroup.tag} (${tagGroup.tagnum}个)`;
                tagTitle.style.gridColumn = '1 / -1';
                container.appendChild(tagTitle);

                tagGroup.List.forEach(material => {
                    const card = createInspirationMaterialCard(material, tagGroup.tag);
                    container.appendChild(card);
                });
            });
        }

        // 创建个人素材卡片
        function createMaterialCard(material, type) {
            const card = document.createElement('div');
            card.className = 'material-card';
            
            card.innerHTML = `
                <h4>${material.title}</h4>
                <img src="${material.photo}" alt="${material.title}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4='">
                <p><span class="type-badge">${type === 1 ? '图片' : '3D模型'}</span> <span class="tag">${material.tag}</span></p>
                <p><small>ID: ${material.guiId}</small></p>
                <p><a href="${material.photourl}" target="_blank">查看原图</a></p>
            `;
            
            return card;
        }

        // 创建灵感广场素材卡片
        function createInspirationMaterialCard(material, tag) {
            const card = document.createElement('div');
            card.className = 'material-card';
            
            card.innerHTML = `
                <h4>${material.title}</h4>
                <img src="${material.photourl}" alt="${material.title}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4='">
                <p><span class="tag">${tag}</span></p>
                <p><small>ID: ${material.guiId}</small></p>
                <p><a href="${material.detailurl}" target="_blank">查看详情</a></p>
            `;
            
            return card;
        }

        // 显示状态信息
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
            element.style.display = 'block';
        }

        // 页面加载时恢复Token
        window.onload = function() {
            const savedToken = localStorage.getItem('demoToken');
            if (savedToken) {
                document.getElementById('tokenInput').value = savedToken;
                currentToken = savedToken;
                showStatus('tokenStatus', 'Token已从本地存储恢复', 'success');
            }
        };
    </script>
</body>
</html>
