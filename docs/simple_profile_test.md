# 简化个人信息接口测试

## 🎯 测试目标

验证 `/system/user/profile/simple` 接口是否正确返回新增的出生日期和个人简介字段。

## 🔧 测试前准备

### 1. 确保数据库已更新
```sql
-- 检查字段是否存在
DESCRIBE sys_user;

-- 如果字段不存在，执行以下SQL
ALTER TABLE sys_user 
ADD COLUMN birth_date DATE DEFAULT NULL COMMENT '出生日期',
ADD COLUMN bio TEXT DEFAULT NULL COMMENT '个人简介';
```

### 2. 添加测试数据
```sql
-- 为admin用户添加测试数据
UPDATE sys_user 
SET birth_date = '1990-01-01', 
    bio = '系统管理员，负责系统维护和用户管理工作。' 
WHERE user_name = 'admin';
```

## 🚀 测试步骤

### 步骤1：获取登录Token
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9..."
}
```

### 步骤2：调用简化个人信息接口
```bash
curl -X GET http://localhost:8080/system/user/profile/simple \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "若依",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "http://localhost:8080/profile/avatar/xxx.jpg",
    "status": "0",
    "delFlag": "0",
    "birthDate": "1990-01-01",
    "bio": "系统管理员，负责系统维护和用户管理工作。"
  }
}
```

### 步骤3：验证字段完整性
检查响应中是否包含以下新增字段：
- ✅ `birthDate`: 出生日期（格式：yyyy-MM-dd）
- ✅ `bio`: 个人简介（字符串类型）

## 🌐 浏览器测试

### 使用浏览器开发者工具
1. 打开浏览器开发者工具（F12）
2. 在Console中执行：

```javascript
// 替换为实际的token
const token = 'YOUR_TOKEN_HERE';

fetch('/system/user/profile/simple', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  console.log('简化个人信息:', data);
  
  // 验证新增字段
  if (data.code === 200) {
    const user = data.data;
    console.log('出生日期:', user.birthDate);
    console.log('个人简介:', user.bio);
    
    // 检查字段是否存在
    if (user.hasOwnProperty('birthDate')) {
      console.log('✅ birthDate字段存在');
    } else {
      console.log('❌ birthDate字段缺失');
    }
    
    if (user.hasOwnProperty('bio')) {
      console.log('✅ bio字段存在');
    } else {
      console.log('❌ bio字段缺失');
    }
  }
})
.catch(error => console.error('请求失败:', error));
```

## 📱 前端集成测试

### Vue.js示例
```javascript
// 在Vue组件中调用
export default {
  data() {
    return {
      userProfile: {}
    }
  },
  methods: {
    async getSimpleProfile() {
      try {
        const response = await this.$http.get('/system/user/profile/simple');
        if (response.code === 200) {
          this.userProfile = response.data;
          console.log('用户信息:', this.userProfile);
          
          // 验证新增字段
          console.log('出生日期:', this.userProfile.birthDate);
          console.log('个人简介:', this.userProfile.bio);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    }
  },
  mounted() {
    this.getSimpleProfile();
  }
}
```

### React示例
```javascript
import { useState, useEffect } from 'react';

function UserProfile() {
  const [userProfile, setUserProfile] = useState({});

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/system/user/profile/simple', {
          headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
          }
        });
        const data = await response.json();
        
        if (data.code === 200) {
          setUserProfile(data.data);
          console.log('用户信息:', data.data);
          console.log('出生日期:', data.data.birthDate);
          console.log('个人简介:', data.data.bio);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };

    fetchProfile();
  }, []);

  return (
    <div>
      <h3>用户信息</h3>
      <p>昵称: {userProfile.nickName}</p>
      <p>邮箱: {userProfile.email}</p>
      <p>出生日期: {userProfile.birthDate}</p>
      <p>个人简介: {userProfile.bio}</p>
    </div>
  );
}
```

## ✅ 验证要点

### 1. 字段完整性检查
- [ ] 原有9个字段是否完整
- [ ] 新增的birthDate字段是否存在
- [ ] 新增的bio字段是否存在

### 2. 数据格式验证
- [ ] birthDate格式是否为yyyy-MM-dd
- [ ] bio字段是否为字符串类型
- [ ] 所有字段类型是否正确

### 3. 空值处理
```sql
-- 测试空值情况
UPDATE sys_user SET birth_date = NULL, bio = NULL WHERE user_name = 'admin';
```
- [ ] birthDate为null时是否正常返回
- [ ] bio为null时是否正常返回

### 4. 性能测试
- [ ] 响应时间是否在可接受范围内
- [ ] 数据量是否比完整接口小

## 🚨 常见问题排查

### 1. 字段不存在
**问题**: 响应中没有birthDate或bio字段
**解决**: 
- 检查数据库表是否添加了新字段
- 检查SysUser实体类是否更新
- 检查Mapper XML是否更新

### 2. 字段值为null
**问题**: 字段存在但值为null
**解决**:
- 检查数据库中是否有数据
- 执行UPDATE语句添加测试数据

### 3. 日期格式问题
**问题**: 日期格式不正确
**解决**:
- 检查@JsonFormat注解是否正确
- 确认数据库中日期格式

### 4. 编译错误
**问题**: 应用启动失败
**解决**:
- 检查SimpleUserProfileDto构造函数参数
- 检查控制器中的方法调用
- 重新编译项目

## 📊 测试结果记录

| 测试项 | 预期结果 | 实际结果 | 状态 |
|--------|----------|----------|------|
| 字段完整性 | 11个字段 | ___ | ⏳ |
| birthDate存在 | 存在 | ___ | ⏳ |
| bio存在 | 存在 | ___ | ⏳ |
| 日期格式 | yyyy-MM-dd | ___ | ⏳ |
| 响应时间 | <200ms | ___ | ⏳ |

完成测试后，请在状态列标记：✅ 通过 / ❌ 失败
