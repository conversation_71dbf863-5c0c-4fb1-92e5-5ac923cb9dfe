# 用户个人资料API测试用例

## 测试环境准备

### 1. 执行数据库脚本
```sql
-- 执行以下SQL添加新字段
ALTER TABLE sys_user 
ADD COLUMN birth_date DATE DEFAULT NULL COMMENT '出生日期',
ADD COLUMN bio TEXT DEFAULT NULL COMMENT '个人简介';
```

### 2. 重启应用
确保应用重启后加载了新的实体类和Mapper配置。

## 测试用例

### 测试用例1：修改个人资料 - 成功场景

**测试目标：** 验证正常修改个人资料功能

**请求方式：** PUT

**请求URL：** `/system/user/profile/updateProfile`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体：**
```json
{
  "nickName": "测试昵称",
  "email": "<EMAIL>",
  "birthDate": "1990-05-15",
  "bio": "这是我的个人简介，用于测试功能。"
}
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "个人资料修改成功",
  "data": null
}
```

### 测试用例2：修改个人资料 - 邮箱重复

**测试目标：** 验证邮箱唯一性校验

**请求体：**
```json
{
  "nickName": "测试昵称",
  "email": "<EMAIL>",
  "birthDate": "1990-05-15",
  "bio": "测试邮箱重复"
}
```

**预期响应：**
```json
{
  "code": 500,
  "msg": "修改用户'admin'失败，邮箱账号已存在",
  "data": null
}
```

### 测试用例3：修改个人资料 - 参数验证

**测试目标：** 验证参数长度限制

**请求体：**
```json
{
  "nickName": "这是一个超过30个字符限制的非常长的用户昵称测试",
  "email": "invalid-email",
  "bio": "这是一个超过500个字符的个人简介..." // 超过500字符的文本
}
```

**预期响应：**
```json
{
  "code": 400,
  "msg": "参数验证失败",
  "data": null
}
```

### 测试用例4：获取个人资料详情

**测试目标：** 验证获取个人资料详情功能

**请求方式：** GET

**请求URL：** `/system/user/profile/detail`

**请求头：**
```
Authorization: Bearer {token}
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "测试昵称",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "",
    "birthDate": "1990-05-15",
    "bio": "这是我的个人简介，用于测试功能。",
    "createTime": "2024-01-01 10:00:00",
    "loginDate": "2024-08-14 18:00:00",
    "roleGroup": "超级管理员",
    "postGroup": "董事长"
  }
}
```

### 测试用例5：部分字段更新

**测试目标：** 验证只更新部分字段的功能

**请求体：**
```json
{
  "nickName": "新昵称",
  "bio": "更新后的个人简介"
}
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "个人资料修改成功",
  "data": null
}
```

### 测试用例6：清空字段

**测试目标：** 验证清空可选字段的功能

**请求体：**
```json
{
  "birthDate": null,
  "bio": ""
}
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "个人资料修改成功",
  "data": null
}
```

## 使用Postman测试

### 1. 导入环境变量
```json
{
  "name": "RuoYi-Test",
  "values": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080",
      "enabled": true
    },
    {
      "key": "token",
      "value": "your-jwt-token-here",
      "enabled": true
    }
  ]
}
```

### 2. 测试步骤
1. 先调用登录接口获取token
2. 设置Authorization头：`Bearer {{token}}`
3. 依次执行上述测试用例
4. 验证响应结果是否符合预期

## 使用curl测试

### 获取token
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 修改个人资料
```bash
curl -X PUT http://localhost:8080/system/user/profile/updateProfile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "nickName": "测试昵称",
    "email": "<EMAIL>",
    "birthDate": "1990-05-15",
    "bio": "这是我的个人简介"
  }'
```

### 获取个人资料详情
```bash
curl -X GET http://localhost:8080/system/user/profile/detail \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 验证要点

1. **数据库验证**：检查sys_user表中对应记录是否正确更新
2. **缓存验证**：验证用户缓存信息是否同步更新
3. **日志验证**：检查系统日志是否正确记录操作
4. **权限验证**：确保只能修改当前登录用户的信息
5. **数据完整性**：验证原有字段不受影响
