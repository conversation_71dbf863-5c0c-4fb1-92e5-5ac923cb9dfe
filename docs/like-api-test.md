# 素材点赞功能接口测试文档

## 1. 接口列表

### 1.1 点赞/取消点赞接口
- **接口地址**: `POST /inspiration/like`
- **权限要求**: `system:inspiration:like`

### 1.2 查询我的喜欢接口
- **接口地址**: `GET /inspiration/likes`
- **权限要求**: `system:inspiration:view`

### 1.3 批量查询点赞状态接口
- **接口地址**: `POST /inspiration/likes/batch`
- **权限要求**: `system:inspiration:view`

## 2. 测试步骤

### 2.1 准备工作

#### 创建数据库表
```sql
-- 执行 sql/user_material_like.sql 文件
CREATE TABLE `user_material_like` (
  `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `material_id` varchar(100) NOT NULL COMMENT '素材ID（guiId）',
  `material_title` varchar(200) DEFAULT NULL COMMENT '素材标题',
  `material_tag` varchar(100) DEFAULT NULL COMMENT '素材标签',
  `photo_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `detail_url` varchar(500) DEFAULT NULL COMMENT '详情图URL',
  `like_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`like_id`),
  UNIQUE KEY `uk_user_material` (`user_id`, `material_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_material_tag` (`material_tag`),
  KEY `idx_like_time` (`like_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户素材点赞表';
```

#### 获取测试Token
```bash
curl -X POST 'http://localhost:8080/login' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2.2 接口测试

#### 测试1：点赞素材
```bash
curl -X POST "http://localhost:8080/inspiration/like" \
  -H "Token: YOUR_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "materialId=2131sdhjdhh&action=like&title=周杰伦&tag=人物&photoUrl=//address//周杰伦.缩略图&detailUrl=//address/周杰伦.jpg"
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "点赞成功",
    "data": {
        "materialId": "2131sdhjdhh",
        "isLiked": true,
        "likeCount": 1
    }
}
```

#### 测试2：取消点赞
```bash
curl -X POST "http://localhost:8080/inspiration/like" \
  -H "Token: YOUR_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "materialId=2131sdhjdhh&action=unlike"
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "取消点赞成功",
    "data": {
        "materialId": "2131sdhjdhh",
        "isLiked": false,
        "likeCount": 0
    }
}
```

#### 测试3：查询我的喜欢
```bash
curl -X GET "http://localhost:8080/inspiration/likes" \
  -H "Token: YOUR_TOKEN"
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "查询成功",
    "rows": [
        {
            "likeId": 1,
            "userId": 1,
            "materialId": "2131sdhjdhh",
            "materialTitle": "周杰伦",
            "materialTag": "人物",
            "photoUrl": "//address//周杰伦.缩略图",
            "detailUrl": "//address/周杰伦.jpg",
            "likeTime": "2025-01-13 10:30:00",
            "createBy": "admin",
            "createTime": "2025-01-13 10:30:00"
        }
    ],
    "total": 1
}
```

#### 测试4：按标签筛选我的喜欢
```bash
curl -X GET "http://localhost:8080/inspiration/likes?tag=人物" \
  -H "Token: YOUR_TOKEN"
```

#### 测试5：批量查询点赞状态
```bash
curl -X POST "http://localhost:8080/inspiration/likes/batch" \
  -H "Token: YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '["2131sdhjdhh", "3242sdfdsf", "4353dfgdfg"]'
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "2131sdhjdhh": true,
        "3242sdfdsf": false,
        "4353dfgdfg": false
    }
}
```

## 3. 测试场景

### 3.1 正常流程测试
1. 用户首次点赞素材 → 成功
2. 用户重复点赞同一素材 → 状态不变
3. 用户取消点赞 → 成功
4. 用户重复取消点赞 → 状态不变

### 3.2 数据完整性测试
1. 点赞后查询我的喜欢 → 能看到点赞的素材
2. 取消点赞后查询我的喜欢 → 看不到该素材
3. 批量查询点赞状态 → 状态正确

### 3.3 权限测试
1. 未登录用户访问接口 → 401错误
2. 无权限用户访问接口 → 403错误

### 3.4 参数验证测试
1. materialId为空 → 400错误
2. action参数错误 → 400错误
3. 批量查询空列表 → 返回空结果

## 4. 性能测试

### 4.1 并发点赞测试
```bash
# 使用ab工具测试并发点赞
ab -n 100 -c 10 -p post_data.txt -T "application/x-www-form-urlencoded" \
   -H "Token: YOUR_TOKEN" \
   "http://localhost:8080/inspiration/like"
```

### 4.2 大量数据查询测试
```bash
# 批量查询大量素材的点赞状态
curl -X POST "http://localhost:8080/inspiration/likes/batch" \
  -H "Token: YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '[大量materialId数组]'
```

## 5. 错误处理测试

### 5.1 数据库连接异常
- 模拟数据库连接失败
- 验证错误响应格式

### 5.2 参数异常
- 传递非法参数
- 验证参数校验逻辑

### 5.3 业务异常
- 用户不存在
- 素材ID格式错误

## 6. 集成测试

### 6.1 与灵感广场接口集成
```bash
# 1. 先调用GetAllContentDetail获取素材列表
curl -X GET "http://localhost:8080/external/api/inspiration/GetAllContentDetail" \
  -H "Token: YOUR_TOKEN"

# 2. 提取guiId进行点赞
curl -X POST "http://localhost:8080/inspiration/like" \
  -H "Token: YOUR_TOKEN" \
  -d "materialId=从上一步获取的guiId&action=like&..."

# 3. 批量查询点赞状态
curl -X POST "http://localhost:8080/inspiration/likes/batch" \
  -H "Token: YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '[从第1步获取的所有guiId]'
```

## 7. 数据验证

### 7.1 数据库数据验证
```sql
-- 查看点赞记录
SELECT * FROM user_material_like WHERE user_id = 1;

-- 统计某素材的点赞数
SELECT COUNT(*) FROM user_material_like WHERE material_id = '2131sdhjdhh';

-- 查看用户的点赞列表
SELECT material_id, material_title, like_time 
FROM user_material_like 
WHERE user_id = 1 
ORDER BY like_time DESC;
```

### 7.2 业务逻辑验证
- 同一用户对同一素材只能有一条点赞记录
- 点赞数统计准确
- 我的喜欢列表按时间倒序排列

## 8. 注意事项

1. **Token有效性**：确保测试用的Token未过期
2. **权限配置**：确保用户有相应的权限
3. **数据清理**：测试完成后清理测试数据
4. **并发安全**：注意测试并发点赞的数据一致性
5. **性能监控**：关注接口响应时间和数据库性能

测试完成后，点赞功能应该能够正常工作，为前端提供完整的点赞API支持！
