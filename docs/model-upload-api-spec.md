# 3D模型上传接口规范文档

## 接口概述

本文档定义了本系统上传3D模型文件到素材广场的接口规范，包括本系统的调用接口和素材广场系统需要实现的接收接口。

---

## 1. 本系统提供的模型上传接口

### 1.1 接口信息
- **接口地址**: `POST /inspiration/uploadModel`
- **功能描述**: 上传3D模型文件和预览图片到素材广场系统
- **权限要求**: `system:inspiration:upload`

### 1.2 请求参数

#### 请求头
```
Token: eyJhbGciOiJIUzUxMiJ9...  (必填，用户登录Token)
Content-Type: multipart/form-data
```

#### 请求体（multipart/form-data）
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| file | File | 是 | 模型预览图片 | preview.jpg |
| modelFile | File | 是 | 3D模型文件 | model.glb |
| title | String | 否 | 素材标题 | "精美建筑模型" |
| description | String | 否 | 素材描述 | "这是一个现代建筑的3D模型" |
| tag | String | 否 | 素材标签 | "建筑" |

#### 文件限制

**预览图片限制**：
- **文件类型**: 只支持图片文件（image/*）
- **文件大小**: 最大10MB
- **支持格式**: JPG, PNG, GIF, BMP, WEBP等

**模型文件限制**：
- **文件类型**: 支持常见3D模型格式
- **文件大小**: 最大50MB
- **支持格式**: .glb, .gltf, .obj, .fbx, .3ds, .dae, .ply

### 1.3 响应格式

#### 成功响应
```json
{
    "code": 200,
    "msg": "模型上传成功",
    "data": {
        "materialId": "model_20250113_001",
        "fileName": "preview.jpg",
        "modelFileName": "building.glb",
        "fileSize": 1048576,
        "modelFileSize": 5242880,
        "uploadTime": "2025-01-13T10:30:00",
        "materialUrl": "http://*************:2283/materials/images/model_20250113_001.jpg",
        "thumbnailUrl": "http://*************:2283/materials/thumbnails/model_20250113_001_thumb.jpg",
        "modelUrl": "http://*************:2283/materials/models/model_20250113_001.glb",
        "type": "model"
    }
}
```

#### 失败响应
```json
{
    "code": 500,
    "msg": "上传失败: 模型文件格式不支持，支持格式：.glb, .gltf, .obj, .fbx, .3ds, .dae, .ply"
}
```

### 1.4 调用示例

#### JavaScript (FormData)
```javascript
const formData = new FormData();
formData.append('file', previewImageFile);        // 预览图片
formData.append('modelFile', modelFile);          // 模型文件
formData.append('title', '精美建筑模型');
formData.append('description', '这是一个现代建筑的3D模型');
formData.append('tag', '建筑');

fetch('/inspiration/uploadModel', {
    method: 'POST',
    headers: {
        'Token': localStorage.getItem('token')
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('模型上传成功:', data.data);
    } else {
        console.error('上传失败:', data.msg);
    }
});
```

#### cURL
```bash
curl -X POST "http://localhost:8080/inspiration/uploadModel" \
  -H "Token: eyJhbGciOiJIUzUxMiJ9..." \
  -F "file=@/path/to/preview.jpg" \
  -F "modelFile=@/path/to/model.glb" \
  -F "title=精美建筑模型" \
  -F "description=这是一个现代建筑的3D模型" \
  -F "tag=建筑"
```

---

## 2. 素材广场系统需要实现的接收接口

### 2.1 接口信息
- **接口地址**: `POST /external/api/inspiration/uploadModel`
- **功能描述**: 接收并处理上传的3D模型文件和预览图片
- **认证方式**: `Authorization: Bearer <token>`

### 2.2 请求参数

#### 请求头
```
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...  (必填)
Content-Type: multipart/form-data
```

#### 请求体（multipart/form-data）
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| file | File | 是 | 模型预览图片 | preview.jpg |
| modelFile | File | 是 | 3D模型文件 | model.glb |
| title | String | 否 | 素材标题 | "精美建筑模型" |
| description | String | 否 | 素材描述 | "这是一个现代建筑的3D模型" |
| tag | String | 否 | 素材标签 | "建筑" |
| type | String | 是 | 素材类型（固定值："model"） | "model" |

### 2.3 处理要求

#### 文件处理
1. **预览图片处理**：
   - 验证图片格式和大小
   - 生成缩略图（建议尺寸：200x200）
   - 存储原始预览图片

2. **模型文件处理**：
   - 验证模型文件格式
   - 验证文件完整性
   - 存储模型文件
   - 可选：生成模型缩略图或预览

3. **数据库记录**：
   - 保存模型素材信息
   - 记录上传用户、时间等信息
   - 生成唯一的素材ID
   - 设置素材类型为"model"

#### 响应要求
```json
{
    "code": 200,
    "msg": "success",
    "materialId": "model_20250113_001",
    "materialUrl": "http://*************:2283/materials/images/model_20250113_001.jpg",
    "thumbnailUrl": "http://*************:2283/materials/thumbnails/model_20250113_001_thumb.jpg",
    "modelUrl": "http://*************:2283/materials/models/model_20250113_001.glb"
}
```

### 2.4 Java实现示例

```java
@RestController
@RequestMapping("/external/api/inspiration")
public class ModelUploadController {

    @PostMapping("/uploadModel")
    public ResponseEntity<?> uploadModel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("modelFile") MultipartFile modelFile,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "tag", required = false) String tag,
            @RequestParam(value = "type", required = false) String type,
            HttpServletRequest request) {
        
        try {
            // 1. 验证认证
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.status(401).body(Map.of("code", 401, "msg", "认证失败"));
            }
            
            String token = authHeader.substring(7);
            // 验证token有效性...
            
            // 2. 验证预览图片
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "预览图片不能为空"));
            }
            
            if (!file.getContentType().startsWith("image/")) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "预览图片只支持图片文件"));
            }
            
            // 3. 验证模型文件
            if (modelFile.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "模型文件不能为空"));
            }
            
            if (!isValidModelFile(modelFile.getOriginalFilename())) {
                return ResponseEntity.badRequest().body(Map.of("code", 400, "msg", "不支持的模型文件格式"));
            }
            
            // 4. 处理文件上传
            String materialId = generateMaterialId();
            
            // 保存预览图片
            String imageExtension = getFileExtension(file.getOriginalFilename());
            String imagePath = "/materials/images/" + materialId + imageExtension;
            file.transferTo(new File(uploadDir + imagePath));
            
            // 生成缩略图
            String thumbnailPath = "/materials/thumbnails/" + materialId + "_thumb" + imageExtension;
            generateThumbnail(uploadDir + imagePath, uploadDir + thumbnailPath);
            
            // 保存模型文件
            String modelExtension = getFileExtension(modelFile.getOriginalFilename());
            String modelPath = "/materials/models/" + materialId + modelExtension;
            modelFile.transferTo(new File(uploadDir + modelPath));
            
            // 5. 保存到数据库
            Material material = new Material();
            material.setMaterialId(materialId);
            material.setTitle(title);
            material.setDescription(description);
            material.setTag(tag);
            material.setType("model");  // 设置为模型类型
            material.setMaterialUrl(baseUrl + imagePath);
            material.setThumbnailUrl(baseUrl + thumbnailPath);
            material.setModelUrl(baseUrl + modelPath);
            material.setUploadTime(new Date());
            materialService.save(material);
            
            // 6. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("msg", "success");
            result.put("materialId", materialId);
            result.put("materialUrl", baseUrl + imagePath);
            result.put("thumbnailUrl", baseUrl + thumbnailPath);
            result.put("modelUrl", baseUrl + modelPath);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("code", 500, "msg", "上传失败: " + e.getMessage()));
        }
    }
    
    private boolean isValidModelFile(String fileName) {
        if (fileName == null) return false;
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".glb") || lowerName.endsWith(".gltf") || 
               lowerName.endsWith(".obj") || lowerName.endsWith(".fbx") || 
               lowerName.endsWith(".3ds") || lowerName.endsWith(".dae") || 
               lowerName.endsWith(".ply");
    }
}
```

---

## 3. 与图片上传接口的区别

| 特性 | 图片上传 | 模型上传 |
|------|----------|----------|
| 接口地址 | `/inspiration/upload` | `/inspiration/uploadModel` |
| 文件参数 | `file` (图片) | `file` (预览图) + `modelFile` (模型) |
| 文件大小限制 | 图片: 10MB | 预览图: 10MB, 模型: 50MB |
| 支持格式 | 图片格式 | 图片 + 3D模型格式 |
| 返回字段 | `materialUrl`, `thumbnailUrl` | `materialUrl`, `thumbnailUrl`, `modelUrl` |
| 类型标识 | 无 | `type: "model"` |

---

## 4. 前端集成示例

```html
<form id="modelUploadForm">
    <div>
        <label>预览图片:</label>
        <input type="file" id="previewFile" accept="image/*" required>
    </div>
    <div>
        <label>模型文件:</label>
        <input type="file" id="modelFile" accept=".glb,.gltf,.obj,.fbx,.3ds,.dae,.ply" required>
    </div>
    <div>
        <label>标题:</label>
        <input type="text" id="title">
    </div>
    <div>
        <label>描述:</label>
        <textarea id="description"></textarea>
    </div>
    <div>
        <label>标签:</label>
        <input type="text" id="tag">
    </div>
    <button type="submit">上传模型</button>
</form>

<script>
document.getElementById('modelUploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('file', document.getElementById('previewFile').files[0]);
    formData.append('modelFile', document.getElementById('modelFile').files[0]);
    formData.append('title', document.getElementById('title').value);
    formData.append('description', document.getElementById('description').value);
    formData.append('tag', document.getElementById('tag').value);
    
    try {
        const response = await fetch('/inspiration/uploadModel', {
            method: 'POST',
            headers: {
                'Token': localStorage.getItem('token')
            },
            body: formData
        });
        
        const result = await response.json();
        if (result.code === 200) {
            alert('模型上传成功！');
            console.log('模型URL:', result.data.modelUrl);
        } else {
            alert('上传失败: ' + result.msg);
        }
    } catch (error) {
        alert('上传失败: ' + error.message);
    }
});
</script>
```

现在您有了完整的3D模型上传功能，支持预览图片和模型文件的同时上传！
