# Token验证问题调试指南

## 问题描述

上传接口遇到"Token验证失败"错误，原因是请求头中的Token格式与预期不符。

## 问题原因

实际的请求头中可能：
1. 没有 "Bearer " 前缀
2. Token在不同的请求头中
3. Token格式不正确

## 修复方案

### ✅ 已修复的Token获取逻辑

```java
// 修复后：更灵活的Token获取
String token = request.getHeader("Token");
if (StringUtils.isEmpty(token)) {
    String authHeader = request.getHeader("Authorization");
    if (StringUtils.isNotEmpty(authHeader)) {
        // 如果包含Bearer前缀，去掉前缀
        if (authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        } else {
            // 直接使用Authorization头的值作为token
            token = authHeader;
        }
    }
}
```

### 支持的Token格式

现在系统支持以下几种Token传递方式：

1. **Token请求头**（推荐）：
   ```
   Token: eyJhbGciOiJIUzUxMiJ9...
   ```

2. **Authorization请求头（带Bearer前缀）**：
   ```
   Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
   ```

3. **Authorization请求头（不带Bearer前缀）**：
   ```
   Authorization: eyJhbGciOiJIUzUxMiJ9...
   ```

## 调试步骤

### 1. 检查实际的请求头

#### 方法1：使用浏览器开发者工具
1. 打开浏览器开发者工具（F12）
2. 切换到 Network 标签
3. 发起上传请求
4. 查看请求详情中的 Request Headers

#### 方法2：使用cURL查看请求头
```bash
# 测试Token请求头
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@test.jpg" \
  -v  # -v 参数显示详细的请求头信息

# 测试Authorization请求头
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.jpg" \
  -v
```

#### 方法3：添加调试日志
在服务层添加临时调试代码：
```java
// 临时调试代码（调试完成后删除）
log.info("=== Token调试信息 ===");
log.info("Token头: {}", request.getHeader("Token"));
log.info("Authorization头: {}", request.getHeader("Authorization"));

// 打印所有请求头
Enumeration<String> headerNames = request.getHeaderNames();
while (headerNames.hasMoreElements()) {
    String headerName = headerNames.nextElement();
    log.info("请求头 {}: {}", headerName, request.getHeader(headerName));
}
log.info("=== Token调试信息结束 ===");
```

### 2. 获取有效Token

#### 方法1：通过登录接口获取
```bash
curl -X POST 'http://localhost:8080/login' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

响应示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1Njc4LTEyMzQtMTIzNC0xMjM0LTEyMzQ1Njc4OTAxMiJ9.abcdefg..."
}
```

#### 方法2：从浏览器获取
1. 登录管理后台
2. 打开开发者工具 → Application → Local Storage
3. 查找 `token` 键的值

### 3. 测试不同的Token传递方式

#### 测试1：使用Token请求头
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: eyJhbGciOiJIUzUxMiJ9..." \
  -F "file=@test.jpg" \
  -F "title=测试图片"
```

#### 测试2：使用Authorization请求头（带Bearer）
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiJ9..." \
  -F "file=@test.jpg" \
  -F "title=测试图片"
```

#### 测试3：使用Authorization请求头（不带Bearer）
```bash
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Authorization: eyJhbGciOiJIUzUxMiJ9..." \
  -F "file=@test.jpg" \
  -F "title=测试图片"
```

## 前端调用示例

### JavaScript (推荐使用Token请求头)
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('title', '测试图片');

// 方式1：使用Token请求头（推荐）
fetch('/inspiration/upload', {
    method: 'POST',
    headers: {
        'Token': localStorage.getItem('token')  // 直接使用token值
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('上传结果:', data);
});

// 方式2：使用Authorization请求头
fetch('/inspiration/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token')
    },
    body: formData
});
```

### Vue.js示例
```javascript
// 在Vue组件中
uploadFile() {
    const formData = new FormData();
    formData.append('file', this.selectedFile);
    formData.append('title', this.title);
    
    // 使用axios
    this.$axios.post('/inspiration/upload', formData, {
        headers: {
            'Token': this.$store.getters.token,  // 从Vuex获取token
            'Content-Type': 'multipart/form-data'
        }
    })
    .then(response => {
        if (response.data.code === 200) {
            this.$message.success('上传成功');
        }
    })
    .catch(error => {
        this.$message.error('上传失败: ' + error.message);
    });
}
```

## 常见问题和解决方案

### 问题1：Token为空
**可能原因**：
- 用户未登录
- Token已过期
- 前端没有正确传递Token

**解决方案**：
1. 检查用户登录状态
2. 重新登录获取新Token
3. 检查前端代码中的Token传递逻辑

### 问题2：Token格式错误
**可能原因**：
- Token被截断
- Token包含特殊字符
- Token编码问题

**解决方案**：
1. 检查Token的完整性
2. 确保Token没有被URL编码
3. 检查Token的长度和格式

### 问题3：请求头名称错误
**可能原因**：
- 使用了错误的请求头名称
- 大小写不匹配

**解决方案**：
1. 使用标准的请求头名称：`Token` 或 `Authorization`
2. 注意大小写敏感

## 验证修复效果

修复后，以下任何一种方式都应该能够成功：

```bash
# 方式1：Token请求头
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Token: YOUR_TOKEN" \
  -F "file=@test.jpg"

# 方式2：Authorization请求头（带Bearer）
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.jpg"

# 方式3：Authorization请求头（不带Bearer）
curl -X POST "http://localhost:8080/inspiration/upload" \
  -H "Authorization: YOUR_TOKEN" \
  -F "file=@test.jpg"
```

现在Token获取逻辑更加灵活，应该能够处理各种Token传递方式！
