# 灵感广场下载功能配置修复

## 🚨 问题描述

在下载素材文件时出现连接超时错误，原因是代码中硬编码了错误的IP地址：

- **错误地址**: `*************:2283`
- **正确地址**: `*************:2283`

## ✅ 解决方案

### 1. 立即修复（已完成）

修改了 `InspirationSquareServiceImpl.java` 中的硬编码IP地址：

```java
// 修复前
return originalUrl.replace("localhost:2283", "*************:2283");

// 修复后  
return originalUrl.replace("localhost:2283", "*************:2283");
```

### 2. 长期解决方案（已实现）

#### A. 添加配置属性

在 `InspirationSquareServiceImpl.java` 中添加了配置属性：

```java
/** Immich服务器地址 */
@Value("${inspiration.square.immich-server:*************:2283}")
private String immichServerAddress;
```

#### B. 更新URL修复方法

```java
private String fixLocalhostUrl(String originalUrl) {
    if (originalUrl.contains("localhost:2283")) {
        // 使用配置文件中的地址，而不是硬编码
        return originalUrl.replace("localhost:2283", immichServerAddress);
    }
    return originalUrl;
}
```

#### C. 配置文件设置

在 `application.yml` 中添加了配置：

```yaml
# 灵感广场配置
inspiration:
  square:
    # 灵感广场系统基础URL
    base-url: http://*************:2283
    # API密钥
    api-key: system_token
    # Immich服务器地址（用于文件下载URL修复）
    immich-server: *************:2283
```

## 🔧 配置说明

### 配置项详解

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `inspiration.square.base-url` | 灵感广场API基础地址 | `http://localhost:9001` | `http://*************:2283` |
| `inspiration.square.api-key` | API访问密钥 | 空 | `system_token` |
| `inspiration.square.immich-server` | Immich服务器地址 | `*************:2283` | `*************:2283` |

### 环境适配

#### 开发环境
```yaml
inspiration:
  square:
    base-url: http://localhost:2283
    immich-server: localhost:2283
```

#### 测试环境
```yaml
inspiration:
  square:
    base-url: http://*************:2283
    immich-server: *************:2283
```

#### 生产环境
```yaml
inspiration:
  square:
    base-url: http://your-production-server:2283
    immich-server: your-production-server:2283
```

## 🚀 测试验证

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载新配置
```

### 2. 测试下载功能
```bash
# 获取token
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 测试下载（使用实际存在的素材ID）
curl "http://localhost:8080/inspiration/download/inspiration_b16372ac-bfea-405e-9946-ebc82bd75605?type=original" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 检查日志
应该看到类似的日志：
```
原始URL: http://localhost:2283/api/assets/xxx/original
修复后URL: http://*************:2283/api/assets/xxx/original
```

## 🔍 故障排查

### 常见问题

#### 1. 仍然连接超时
**检查项**：
- [ ] 确认 `*************:2283` 服务是否正常运行
- [ ] 检查网络连接是否正常
- [ ] 验证防火墙设置

**测试命令**：
```bash
# 测试网络连通性
ping *************

# 测试端口连通性
telnet ************* 2283

# 测试HTTP服务
curl http://*************:2283/api/server-info
```

#### 2. 配置未生效
**检查项**：
- [ ] 确认应用已重启
- [ ] 检查配置文件语法是否正确
- [ ] 验证配置文件路径

**验证方法**：
在代码中添加日志输出配置值：
```java
log.info("当前Immich服务器配置: {}", immichServerAddress);
```

#### 3. 认证失败
**检查项**：
- [ ] 确认API密钥是否正确
- [ ] 检查token是否有效

### 日志分析

#### 正常日志
```
INFO - 原始URL: http://localhost:2283/api/assets/xxx/original
INFO - 修复后URL: http://*************:2283/api/assets/xxx/original
INFO - 添加认证头: Authorization: Bearer system_token
```

#### 异常日志
```
ERROR - 下载素材文件失败
java.net.SocketTimeoutException: Connect timed out
```

## 📝 维护建议

### 1. 配置管理
- 使用配置文件管理所有外部服务地址
- 避免在代码中硬编码IP地址
- 为不同环境准备不同的配置文件

### 2. 监控告警
- 添加下载功能的健康检查
- 设置连接超时告警
- 监控下载成功率

### 3. 容错处理
- 增加重试机制
- 添加降级策略
- 提供更友好的错误提示

## 🎯 后续优化

### 1. 服务发现
考虑使用服务发现机制，自动获取Immich服务地址：
```yaml
inspiration:
  square:
    service-discovery:
      enabled: true
      service-name: immich-server
```

### 2. 负载均衡
支持多个Immich服务器：
```yaml
inspiration:
  square:
    immich-servers:
      - *************:2283
      - *************:2283
```

### 3. 缓存优化
添加文件缓存机制，减少重复下载：
```yaml
inspiration:
  square:
    cache:
      enabled: true
      max-size: 1GB
      ttl: 24h
```

现在问题已经修复，重启应用后下载功能应该正常工作了！
