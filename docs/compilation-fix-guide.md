# 编译问题修复指南

## 问题描述
遇到以下编译错误：
- `程序包com.ruoyi.common.annotation不存在`
- `程序包com.ruoyi.common.core.domain不存在`
- `找不到符号`

## 问题原因
1. **模块编译顺序错误**：`ruoyi-common` 需要在其他模块之前编译
2. **IDE缓存问题**：IDE可能缓存了错误的编译状态
3. **JDK环境问题**：需要JDK而不是JRE

## 解决方案

### 方案1：修复Maven模块编译顺序 ✅
已修复根目录 `pom.xml` 中的模块顺序：
```xml
<modules>
    <module>ruoyi-common</module>      <!-- 基础模块，最先编译 -->
    <module>ruoyi-system</module>      <!-- 依赖common -->
    <module>ruoyi-framework</module>   <!-- 依赖common和system -->
    <module>ruoyi-quartz</module>      <!-- 依赖common和system -->
    <module>ruoyi-generator</module>   <!-- 依赖common和system -->
    <module>ruoyi-admin</module>       <!-- 主模块，最后编译 -->
</modules>
```

### 方案2：清理并重新编译
```bash
# 1. 清理所有编译产物
mvn clean

# 2. 只编译common模块
cd ruoyi-common
mvn compile
cd ..

# 3. 编译system模块
cd ruoyi-system
mvn compile
cd ..

# 4. 编译整个项目
mvn compile -DskipTests
```

### 方案3：IDE解决方案

#### IntelliJ IDEA
1. **清理缓存**：
   - File → Invalidate Caches and Restart
   - 选择 "Invalidate and Restart"

2. **重新导入项目**：
   - File → Close Project
   - 重新打开项目
   - 等待Maven自动导入

3. **手动刷新**：
   - 右键项目根目录 → Maven → Reload Projects

#### Eclipse
1. **清理项目**：
   - Project → Clean → Clean all projects

2. **刷新Maven**：
   - 右键项目 → Maven → Reload Projects

### 方案4：检查JDK配置
确保使用JDK而不是JRE：
```bash
# 检查Java版本
java -version
javac -version

# 设置JAVA_HOME（Windows）
set JAVA_HOME=C:\Program Files\Java\jdk-8

# 设置JAVA_HOME（Linux/Mac）
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk
```

## 验证修复

### 1. 检查依赖关系
```bash
# 查看模块依赖
mvn dependency:tree -pl ruoyi-system
```

### 2. 单独编译测试
```bash
# 测试common模块
cd ruoyi-common && mvn compile

# 测试system模块
cd ruoyi-system && mvn compile
```

### 3. 检查类路径
确保以下类存在：
- `com.ruoyi.common.core.domain.BaseEntity`
- `com.ruoyi.common.annotation.Excel`
- `com.ruoyi.common.utils.StringUtils`

## 常见错误和解决方法

### 错误1：`程序包不存在`
**原因**：模块编译顺序错误或依赖缺失
**解决**：按正确顺序编译模块

### 错误2：`找不到符号`
**原因**：类路径问题或IDE缓存
**解决**：清理缓存并重新编译

### 错误3：`NoSuchFieldError`
**原因**：Java版本兼容性问题
**解决**：使用正确的JDK版本

## 最终检查清单

- [ ] Maven模块顺序已修复
- [ ] 使用JDK而不是JRE
- [ ] IDE缓存已清理
- [ ] 项目已重新导入
- [ ] 所有模块可以单独编译
- [ ] 整个项目可以编译成功

## 如果问题仍然存在

1. **检查网络连接**：确保可以下载Maven依赖
2. **检查Maven配置**：确保settings.xml配置正确
3. **检查磁盘空间**：确保有足够的空间存储编译产物
4. **重新克隆项目**：如果以上都不行，重新克隆代码库

编译成功后，所有的外部系统管理功能都应该可以正常工作！
