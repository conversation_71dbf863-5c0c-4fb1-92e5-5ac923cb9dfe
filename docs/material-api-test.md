# 素材管理接口测试指南

## 测试准备

### 1. 获取Token
首先需要登录获取Token：

```bash
curl -X POST \
  'http://localhost:8080/login' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

响应示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1Njc4LTEyMzQtMTIzNC0xMjM0LTEyMzQ1Njc4OTAxMiJ9.abcdefg..."
}
```

### 2. 提取Token
从登录响应中提取token值，用于后续接口调用。

---

## 接口测试

### 测试1: 获取我的创作素材

```bash
curl -X GET \
  'http://localhost:8080/GetAllPersonDetail' \
  -H 'Token: 你的token值'
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "type": 1,
            "List": [
                {
                    "title": "我的创作图片1",
                    "photo": "http://localhost:8080/profile/materials/thumbnails/image1_thumb.jpg",
                    "photourl": "http://localhost:8080/profile/materials/images/image1.jpg",
                    "tag": "人物",
                    "guiId": "personal_img_1704441600000_1"
                },
                {
                    "title": "我的创作图片2",
                    "photo": "http://localhost:8080/profile/materials/thumbnails/image2_thumb.jpg",
                    "photourl": "http://localhost:8080/profile/materials/images/image2.jpg",
                    "tag": "风景",
                    "guiId": "personal_img_1704441600000_2"
                }
            ]
        },
        {
            "type": 2,
            "List": [
                {
                    "title": "我的3D模型1",
                    "photo": "http://localhost:8080/profile/materials/thumbnails/model1_thumb.jpg",
                    "photourl": "http://localhost:8080/profile/materials/models/model1.glb",
                    "tag": "建筑",
                    "guiId": "personal_model_1704441600000_1"
                }
            ]
        }
    ]
}
```

### 测试2: 获取灵感广场素材

```bash
curl -X GET \
  'http://localhost:8080/GetAllContentDetail?userId=admin' \
  -H 'Token: 你的token值'
```

**预期响应**：
```json
{
    "code": 200,
    "msg": "success",
    "data": [
        {
            "tag": "人物",
            "tagnum": 2,
            "List": [
                {
                    "title": "经典人物肖像",
                    "photourl": "http://example.com/thumbnails/portrait1.jpg",
                    "detailurl": "http://example.com/images/portrait1_detail.jpg",
                    "guiId": "inspiration_default_1"
                },
                {
                    "title": "现代人物设计",
                    "photourl": "http://example.com/thumbnails/portrait2.jpg",
                    "detailurl": "http://example.com/images/portrait2_detail.jpg",
                    "guiId": "inspiration_default_2"
                }
            ]
        },
        {
            "tag": "风景",
            "tagnum": 2,
            "List": [
                {
                    "title": "山水风光",
                    "photourl": "http://example.com/thumbnails/landscape1.jpg",
                    "detailurl": "http://example.com/images/landscape1_detail.jpg",
                    "guiId": "inspiration_default_3"
                },
                {
                    "title": "城市夜景",
                    "photourl": "http://example.com/thumbnails/cityscape1.jpg",
                    "detailurl": "http://example.com/images/cityscape1_detail.jpg",
                    "guiId": "inspiration_default_4"
                }
            ]
        },
        {
            "tag": "建筑",
            "tagnum": 2,
            "List": [
                {
                    "title": "古典建筑",
                    "photourl": "http://example.com/thumbnails/architecture1.jpg",
                    "detailurl": "http://example.com/images/architecture1_detail.jpg",
                    "guiId": "inspiration_default_5"
                },
                {
                    "title": "现代建筑",
                    "photourl": "http://example.com/thumbnails/architecture2.jpg",
                    "detailurl": "http://example.com/images/architecture2_detail.jpg",
                    "guiId": "inspiration_default_6"
                }
            ]
        }
    ]
}
```

---

## 错误场景测试

### 测试3: Token无效

```bash
curl -X GET \
  'http://localhost:8080/GetAllPersonDetail' \
  -H 'Token: invalid_token'
```

**预期响应**：
```json
{
    "code": 401,
    "msg": "Token无效或已过期"
}
```

### 测试4: 缺少userId参数

```bash
curl -X GET \
  'http://localhost:8080/GetAllContentDetail' \
  -H 'Token: 你的token值'
```

**预期响应**：
```json
{
    "code": 400,
    "msg": "用户ID不能为空"
}
```

### 测试5: 缺少Token

```bash
curl -X GET \
  'http://localhost:8080/GetAllPersonDetail'
```

**预期响应**：
```json
{
    "code": 401,
    "msg": "Token无效或已过期"
}
```

---

## JavaScript测试示例

### 使用Fetch API

```javascript
// 获取个人创作素材
async function getPersonalMaterials() {
    const token = localStorage.getItem('token'); // 从本地存储获取token
    
    try {
        const response = await fetch('/GetAllPersonDetail', {
            method: 'GET',
            headers: {
                'Token': token
            }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('个人创作素材:', result.data);
            return result.data;
        } else {
            console.error('获取失败:', result.msg);
        }
    } catch (error) {
        console.error('请求错误:', error);
    }
}

// 获取灵感广场素材
async function getInspirationMaterials(userId) {
    const token = localStorage.getItem('token');
    
    try {
        const response = await fetch(`/GetAllContentDetail?userId=${userId}`, {
            method: 'GET',
            headers: {
                'Token': token
            }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('灵感广场素材:', result.data);
            return result.data;
        } else {
            console.error('获取失败:', result.msg);
        }
    } catch (error) {
        console.error('请求错误:', error);
    }
}

// 使用示例
getPersonalMaterials();
getInspirationMaterials('admin');
```

---

## 验证要点

### 1. 数据结构验证
- ✅ 个人创作按type分组（1=图片，2=3D模型）
- ✅ 灵感广场按tag分组，包含tagnum字段
- ✅ 所有URL都是完整的可访问地址
- ✅ guiId字段唯一且有意义

### 2. 认证验证
- ✅ Token验证正常工作
- ✅ 无效Token返回401错误
- ✅ 缺少Token返回401错误

### 3. 参数验证
- ✅ 必填参数缺失时返回400错误
- ✅ 参数格式正确时正常处理

### 4. 容错验证
- ✅ 远程系统不可用时使用模拟数据
- ✅ 异常情况下返回合适的错误信息

---

## 性能测试

### 并发测试
```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 -H "Token: 你的token值" http://localhost:8080/GetAllPersonDetail
```

### 响应时间测试
```bash
# 使用curl测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s \
  -H "Token: 你的token值" \
  http://localhost:8080/GetAllPersonDetail
```

curl-format.txt内容：
```
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
```

测试完成后，接口应该能够正常返回符合领导要求格式的数据！
