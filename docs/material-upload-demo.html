<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>素材上传演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload:hover {
            border-color: #3498db;
            background: #f0f8ff;
        }
        .file-upload.dragover {
            border-color: #3498db;
            background: #e3f2fd;
        }
        .file-upload input[type="file"] {
            display: none;
        }
        .upload-icon {
            font-size: 48px;
            color: #bbb;
            margin-bottom: 10px;
        }
        .upload-text {
            color: #666;
            margin-bottom: 10px;
        }
        .file-info {
            margin-top: 15px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            display: none;
        }
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            margin-top: 10px;
            border-radius: 4px;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #3498db;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>素材上传到灵感广场</h1>
            <p>支持JPG、PNG、GIF等图片格式，最大10MB</p>
        </div>

        <form id="uploadForm">
            <!-- 文件上传区域 -->
            <div class="form-group">
                <label>选择图片文件 *</label>
                <div class="file-upload" id="fileUpload">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                    <div class="upload-text">支持 JPG, PNG, GIF 格式，最大 10MB</div>
                    <input type="file" id="fileInput" accept="image/*" required>
                </div>
                <div class="file-info" id="fileInfo">
                    <div id="fileName"></div>
                    <div id="fileSize"></div>
                    <img id="previewImage" class="preview-image" style="display:none;">
                </div>
            </div>

            <!-- 素材信息 -->
            <div class="form-group">
                <label for="title">素材标题</label>
                <input type="text" id="title" placeholder="请输入素材标题">
            </div>

            <div class="form-group">
                <label for="description">素材描述</label>
                <textarea id="description" placeholder="请输入素材描述"></textarea>
            </div>

            <div class="form-group">
                <label for="category">素材分类</label>
                <select id="category">
                    <option value="">请选择分类</option>
                    <option value="人物">人物</option>
                    <option value="风景">风景</option>
                    <option value="建筑">建筑</option>
                    <option value="动物">动物</option>
                    <option value="植物">植物</option>
                    <option value="其他">其他</option>
                </select>
            </div>

            <div class="form-group">
                <label for="tags">素材标签</label>
                <input type="text" id="tags" placeholder="多个标签用逗号分隔，如：风景,自然,山水">
            </div>

            <!-- 上传按钮 -->
            <div style="text-align: center;">
                <button type="button" class="btn" onclick="resetForm()">重置</button>
                <button type="submit" class="btn btn-success" id="uploadBtn">上传到素材广场</button>
            </div>

            <!-- 进度条 -->
            <div class="progress" id="progressBar">
                <div class="progress-bar" id="progressBarFill"></div>
            </div>

            <!-- 结果显示 -->
            <div class="result" id="result"></div>
        </form>
    </div>

    <script>
        const fileUpload = document.getElementById('fileUpload');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const previewImage = document.getElementById('previewImage');
        const uploadForm = document.getElementById('uploadForm');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressBar = document.getElementById('progressBar');
        const progressBarFill = document.getElementById('progressBarFill');
        const result = document.getElementById('result');

        // 文件上传区域点击事件
        fileUpload.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', handleFileSelect);

        // 拖拽事件
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });

        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });

        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // 处理文件选择
        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                // 验证文件类型
                if (!file.type.startsWith('image/')) {
                    showResult('只支持图片文件！', 'error');
                    return;
                }

                // 验证文件大小
                if (file.size > 10 * 1024 * 1024) {
                    showResult('文件大小不能超过10MB！', 'error');
                    return;
                }

                // 显示文件信息
                fileName.textContent = `文件名：${file.name}`;
                fileSize.textContent = `文件大小：${formatFileSize(file.size)}`;
                fileInfo.style.display = 'block';

                // 显示图片预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImage.src = e.target.result;
                    previewImage.style.display = 'block';
                };
                reader.readAsDataURL(file);

                // 自动填充标题
                if (!document.getElementById('title').value) {
                    const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
                    document.getElementById('title').value = nameWithoutExt;
                }
            }
        }

        // 表单提交事件
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const file = fileInput.files[0];
            if (!file) {
                showResult('请选择要上传的文件！', 'error');
                return;
            }

            // 获取Token
            const token = localStorage.getItem('token') || prompt('请输入登录Token:');
            if (!token) {
                showResult('请先登录获取Token！', 'error');
                return;
            }

            // 构建FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('title', document.getElementById('title').value);
            formData.append('description', document.getElementById('description').value);
            formData.append('category', document.getElementById('category').value);
            formData.append('tags', document.getElementById('tags').value);

            // 显示进度条
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            progressBar.style.display = 'block';
            result.style.display = 'none';

            try {
                const response = await fetch('/inspiration/upload', {
                    method: 'POST',
                    headers: {
                        'Token': token
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.code === 200) {
                    showResult(`上传成功！<br>
                        素材ID: ${data.data.materialId}<br>
                        文件名: ${data.data.fileName}<br>
                        上传时间: ${data.data.uploadTime}<br>
                        <a href="${data.data.materialUrl}" target="_blank">查看原图</a> | 
                        <a href="${data.data.thumbnailUrl}" target="_blank">查看缩略图</a>`, 'success');
                } else {
                    showResult(`上传失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                showResult(`上传失败: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传到素材广场';
                progressBar.style.display = 'none';
            }
        });

        // 显示结果
        function showResult(message, type) {
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        // 重置表单
        function resetForm() {
            uploadForm.reset();
            fileInfo.style.display = 'none';
            previewImage.style.display = 'none';
            result.style.display = 'none';
            progressBar.style.display = 'none';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
