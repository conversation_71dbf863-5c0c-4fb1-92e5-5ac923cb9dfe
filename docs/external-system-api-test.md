# 外部系统管理接口测试

## 问题排查

### 1. 检查接口是否存在
```bash
# 获取所有外部系统列表
curl -X GET "http://localhost:8080/system/external/list" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查看具体的外部系统
curl -X GET "http://localhost:8080/system/external/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 测试重新生成API密钥接口
```bash
# 正确的接口路径
curl -X POST "http://localhost:8080/system/external/regenerateApiKey/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. 检查权限配置
确保用户具有以下权限：
- `system:external:edit` - 修改外部系统权限

### 4. 检查数据库
确保外部系统记录存在：
```sql
SELECT * FROM external_system WHERE system_id = 1;
```

## 可能的解决方案

### 方案1：检查控制器扫描
确保 `SysExternalSystemController` 被正确扫描到：

1. 检查 `@RestController` 注解
2. 检查 `@RequestMapping("/system/external")` 注解
3. 检查包扫描路径

### 方案2：重启应用
如果是新添加的接口，可能需要重启应用：
```bash
# 停止应用
# 重新启动应用
```

### 方案3：检查日志
查看启动日志中是否有相关错误：
```bash
grep -i "external" logs/application.log
grep -i "regenerate" logs/application.log
```

## 接口详细信息

### 重新生成API密钥接口
- **URL**: `POST /system/external/regenerateApiKey/{systemId}`
- **权限**: `system:external:edit`
- **参数**: systemId (路径参数)
- **返回**: 
```json
{
  "code": 200,
  "msg": "API密钥重新生成成功",
  "data": {
    "systemId": 1,
    "systemName": "灵感广场系统",
    "apiKey": "新生成的API密钥",
    "apiSecret": "新生成的API密钥密文",
    // ... 其他字段
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "API密钥重新生成失败"
}
```

## 前端调用示例

### JavaScript
```javascript
// 使用axios调用
axios.post('/system/external/regenerateApiKey/1', {}, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
}).then(response => {
  console.log('API密钥重新生成成功:', response.data);
}).catch(error => {
  console.error('重新生成失败:', error);
});
```

### Vue组件中的调用
```javascript
// 在Vue组件的methods中
handleRegenerate(row) {
  this.$modal.confirm('是否确认重新生成"' + row.systemName + '"的API密钥？').then(() => {
    return regenerateApiKey(row.systemId);
  }).then((response) => {
    this.$modal.msgSuccess("API密钥重新生成成功");
    this.getList(); // 刷新列表
  }).catch(() => {});
}
```
