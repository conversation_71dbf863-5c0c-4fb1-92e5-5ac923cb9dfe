# 下载接口认证修复

## 🚨 问题描述

下载素材文件时出现401认证错误：

```
java.io.IOException: Server returned HTTP response code: 401 for URL: http://*************:2283/api/assets/xxx/original
```

**根本原因**：下载接口使用固定的`system_token`，而Immich服务器要求使用用户的实际token进行认证。

## ✅ 解决方案

### 1. 修改认证方式

#### 修改前（错误）：
```java
// 固定使用系统token
connection.setRequestProperty("Authorization", "Bearer system_token");
```

#### 修改后（正确）：
```java
// 使用当前用户的token
String userToken = getCurrentUserToken();
if (StringUtils.isNotEmpty(userToken)) {
    connection.setRequestProperty("Authorization", "Bearer " + userToken);
} else {
    // 备用方案
    connection.setRequestProperty("Authorization", "Bearer system_token");
}
```

### 2. 添加用户token获取方法

```java
/**
 * 获取当前用户的token
 */
private String getCurrentUserToken() {
    try {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof LoginUser) {
            LoginUser loginUser = (LoginUser) authentication.getPrincipal();
            return loginUser.getToken();
        }
    } catch (Exception e) {
        log.warn("获取用户token失败: {}", e.getMessage());
    }
    return null;
}
```

### 3. 更新方法签名

```java
// 修改前
private void downloadAndTransferFile(String fileUrl, Map<String, Object> materialInfo, String type, HttpServletResponse response)

// 修改后  
private void downloadAndTransferFile(String fileUrl, Map<String, Object> materialInfo, String type, HttpServletResponse response, String userToken)
```

## 🔧 修复内容总结

### A. 文件修改
- ✅ 修改了 `downloadAndTransferFile()` 方法，支持传入用户token
- ✅ 修改了 `downloadMaterial()` 方法，获取并传递用户token
- ✅ 添加了 `getCurrentUserToken()` 方法获取当前用户token
- ✅ 添加了必要的导入类

### B. 认证流程
1. **获取用户token**：从Spring Security上下文获取当前登录用户的token
2. **传递token**：将用户token传递给文件下载方法
3. **设置认证头**：使用用户token设置HTTP请求的Authorization头
4. **备用方案**：如果获取用户token失败，使用系统token作为备用

## 🚀 测试验证

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载新代码
```

### 2. 获取用户token
```bash
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjkzODE1YzEtNzJiOC00OGY0LWJhZWMtMzQzMmRjMTYyNGYxIn0.YCFx6Gkw1uQY_eNcZDyJdiN-rb_rGjPCnaKZxt4GScHO7iB3r4_sDctzac7y2ftsA5egehyBD0xwXOyzV1vY9Q"
}
```

### 3. 测试下载功能
```bash
# 使用获取到的token测试下载
curl "http://localhost:8080/inspiration/download/inspiration_b16372ac-bfea-405e-9946-ebc82bd75605?type=original" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjkzODE1YzEtNzJiOC00OGY0LWJhZWMtMzQzMmRjMTYyNGYxIn0.YCFx6Gkw1uQY_eNcZDyJdiN-rb_rGjPCnaKZxt4GScHO7iB3r4_sDctzac7y2ftsA5egehyBD0xwXOyzV1vY9Q"
```

### 4. 检查日志
应该看到类似的成功日志：
```
INFO - 原始URL: http://localhost:2283/api/assets/xxx/original
INFO - 修复后URL: http://*************:2283/api/assets/xxx/original  
INFO - 添加认证头: Authorization: Bearer eyJhbGciOiJ...
INFO - 文件下载成功，开始传输...
```

## 🔍 故障排查

### 常见问题

#### 1. 仍然401错误
**可能原因**：
- [ ] 用户token已过期
- [ ] token格式不正确
- [ ] Immich服务器认证配置问题

**解决方法**：
```bash
# 重新登录获取新token
curl -X POST http://localhost:8080/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### 2. 获取用户token失败
**检查项**：
- [ ] 确认用户已登录
- [ ] 检查Spring Security配置
- [ ] 验证LoginUser对象是否正确

**调试方法**：
在代码中添加调试日志：
```java
log.info("当前认证对象: {}", authentication);
log.info("用户token: {}", userToken);
```

#### 3. 网络连接问题
**检查项**：
- [ ] 确认IP地址正确（*************:2283）
- [ ] 测试网络连通性
- [ ] 检查防火墙设置

### 日志分析

#### 成功日志
```
INFO - 获取当前用户token成功
INFO - 原始URL: http://localhost:2283/api/assets/xxx/original
INFO - 修复后URL: http://*************:2283/api/assets/xxx/original
INFO - 添加认证头: Authorization: Bearer eyJhbGciOiJ...
INFO - 文件下载成功，大小: 1024KB
```

#### 失败日志
```
WARN - 获取用户token失败: xxx
INFO - 使用系统token作为备用认证
ERROR - 下载素材文件失败: 401 Unauthorized
```

## 📝 技术说明

### 认证机制对比

| 接口类型 | 认证方式 | Token来源 | 说明 |
|----------|----------|-----------|------|
| 获取素材列表 | 系统token | 固定配置 | 查询公开数据，使用系统token |
| 上传文件 | 用户token | 请求头 | 需要用户权限，使用用户token |
| 下载文件 | 用户token | 当前用户 | 需要用户权限，使用用户token |

### 安全考虑

1. **权限控制**：使用用户token确保只有有权限的用户才能下载文件
2. **token安全**：避免在日志中完整输出token，只显示前几位
3. **备用方案**：提供系统token作为备用，确保服务可用性
4. **错误处理**：完善的异常处理和错误提示

## 🎯 后续优化

### 1. 缓存优化
考虑缓存用户token，避免重复获取：
```java
@Cacheable(value = "userTokens", key = "#userId")
public String getUserToken(String userId) {
    // 获取用户token逻辑
}
```

### 2. 重试机制
添加token刷新和重试机制：
```java
if (response.getResponseCode() == 401) {
    // 刷新token并重试
    String newToken = refreshUserToken();
    // 重新发起请求
}
```

### 3. 监控告警
添加下载成功率监控：
```java
// 记录下载成功/失败统计
downloadMetrics.increment("download.success");
downloadMetrics.increment("download.failure");
```

现在认证问题已经修复，重启应用后下载功能应该正常工作了！
