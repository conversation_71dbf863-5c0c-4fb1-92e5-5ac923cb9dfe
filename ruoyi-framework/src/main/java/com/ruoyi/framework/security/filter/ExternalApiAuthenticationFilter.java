package com.ruoyi.framework.security.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.ExternalApiLog;
import com.ruoyi.system.domain.ExternalSystem;
import com.ruoyi.system.service.IExternalApiLogService;
import com.ruoyi.system.service.IExternalApiAuthService;

/**
 * 外部API认证过滤器
 * 
 * <AUTHOR>
 */
@Component
public class ExternalApiAuthenticationFilter extends OncePerRequestFilter
{
    private static final Logger log = LoggerFactory.getLogger(ExternalApiAuthenticationFilter.class);

    @Autowired
    private IExternalApiAuthService externalApiAuthService;

    @Autowired
    private IExternalApiLogService externalApiLogService;

    /** 外部API路径前缀 */
    private static final String EXTERNAL_API_PREFIX = "/external/api";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException
    {
        String requestUri = request.getRequestURI();
        
        // 只对外部API路径进行认证
        if (!requestUri.startsWith(EXTERNAL_API_PREFIX))
        {
            filterChain.doFilter(request, response);
            return;
        }

        long startTime = System.currentTimeMillis();
        ExternalApiLog apiLog = new ExternalApiLog();
        
        try
        {
            // 记录请求信息
            recordRequestInfo(request, apiLog);
            
            // 验证API密钥
            String apiKey = request.getHeader("X-API-Key");
            String signature = request.getHeader("X-Signature");
            String timestamp = request.getHeader("X-Timestamp");
            
            if (StringUtils.isEmpty(apiKey))
            {
                handleAuthFailure(response, apiLog, startTime, "缺少API密钥");
                return;
            }
            
            // 验证系统配置
            ExternalSystem externalSystem = externalApiAuthService.validateApiKey(apiKey);
            if (externalSystem == null)
            {
                handleAuthFailure(response, apiLog, startTime, "无效的API密钥");
                return;
            }
            
            apiLog.setSystemId(externalSystem.getSystemId());
            apiLog.setSystemName(externalSystem.getSystemName());
            
            // 验证IP白名单
            String clientIp = IpUtils.getIpAddr(request);
            if (!validateIpWhitelist(clientIp, externalSystem.getAllowedIps()))
            {
                handleAuthFailure(response, apiLog, startTime, "IP地址不在白名单中: " + clientIp);
                return;
            }
            
            // 验证请求签名
            if (!externalApiAuthService.validateSignature(request, externalSystem.getApiSecret(), signature, timestamp))
            {
                handleAuthFailure(response, apiLog, startTime, "请求签名验证失败");
                return;
            }
            
            // 验证限流
            if (!externalApiAuthService.checkRateLimit(externalSystem.getSystemCode(), externalSystem.getRateLimit()))
            {
                handleAuthFailure(response, apiLog, startTime, "请求频率超过限制");
                return;
            }
            
            // 设置系统信息到请求属性中
            request.setAttribute("externalSystem", externalSystem);
            
            // 继续处理请求
            filterChain.doFilter(request, response);
            
            // 记录成功日志
            recordSuccessLog(apiLog, startTime);
            
        }
        catch (Exception e)
        {
            log.error("外部API认证过程中发生异常", e);
            handleAuthFailure(response, apiLog, startTime, "认证过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 记录请求信息
     */
    private void recordRequestInfo(HttpServletRequest request, ExternalApiLog apiLog)
    {
        apiLog.setRequestUrl(request.getRequestURL().toString());
        apiLog.setRequestMethod(request.getMethod());
        apiLog.setRequestIp(IpUtils.getIpAddr(request));
        apiLog.setUserAgent(request.getHeader("User-Agent"));
        apiLog.setRequestTime(new Date());
        
        // 记录请求头（敏感信息需要脱敏）
        StringBuilder headers = new StringBuilder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements())
        {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            if ("X-API-Key".equals(headerName) || "X-Signature".equals(headerName))
            {
                headerValue = "***"; // 脱敏处理
            }
            headers.append(headerName).append(": ").append(headerValue).append("\n");
        }
        apiLog.setRequestHeaders(headers.toString());
        
        // 记录请求参数
        if ("GET".equals(request.getMethod()))
        {
            apiLog.setRequestParams(request.getQueryString());
        }
    }

    /**
     * 验证IP白名单
     */
    private boolean validateIpWhitelist(String clientIp, String allowedIps)
    {
        if (StringUtils.isEmpty(allowedIps))
        {
            return true; // 如果没有配置白名单，则允许所有IP
        }
        
        List<String> ipList = Arrays.asList(allowedIps.split(","));
        return ipList.contains(clientIp.trim()) || ipList.contains("*");
    }

    /**
     * 处理认证失败
     */
    private void handleAuthFailure(HttpServletResponse response, ExternalApiLog apiLog, long startTime, String errorMessage)
            throws IOException
    {
        apiLog.setIsSuccess("0");
        apiLog.setErrorMessage(errorMessage);
        apiLog.setResponseStatus(401);
        apiLog.setDuration(System.currentTimeMillis() - startTime);
        
        // 异步记录日志
        try
        {
            externalApiLogService.insertExternalApiLog(apiLog);
        }
        catch (Exception e)
        {
            log.error("记录API调用日志失败", e);
        }
        
        // 返回认证失败响应
        AjaxResult result = AjaxResult.error(401, errorMessage);
        ServletUtils.renderString(response, JSON.toJSONString(result));
    }

    /**
     * 记录成功日志
     */
    private void recordSuccessLog(ExternalApiLog apiLog, long startTime)
    {
        apiLog.setIsSuccess("1");
        apiLog.setResponseStatus(200);
        apiLog.setDuration(System.currentTimeMillis() - startTime);
        
        // 异步记录日志
        try
        {
            externalApiLogService.insertExternalApiLog(apiLog);
        }
        catch (Exception e)
        {
            log.error("记录API调用日志失败", e);
        }
    }
}
