package com.ruoyi.framework.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;

/**
 * 文件上传配置
 * 
 * <AUTHOR>
 */
@Configuration
public class MultipartConfig
{
    /**
     * 文件上传配置
     */
    @Bean
    public MultipartConfigElement multipartConfigElement()
    {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 单个文件最大大小：50MB（支持模型文件）
        factory.setMaxFileSize(DataSize.ofMegabytes(50));
        
        // 总上传数据最大大小：100MB（支持同时上传预览图和模型文件）
        factory.setMaxRequestSize(DataSize.ofMegabytes(100));
        
        return factory.createMultipartConfig();
    }
}
