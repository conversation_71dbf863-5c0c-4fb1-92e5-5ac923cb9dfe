package com.example.sdk.example;

import java.util.List;
import com.example.sdk.InspirationSquareApiClient;
import com.example.sdk.model.ApiResponse;
import com.example.sdk.model.InspirationCategory;
import com.example.sdk.model.InspirationMaterial;
import com.example.sdk.model.PageResult;

/**
 * 灵感广场API客户端使用示例
 * 
 * <AUTHOR>
 */
public class ApiClientExample
{
    public static void main(String[] args)
    {
        // 初始化API客户端
        String baseUrl = "http://localhost:8080";
        String apiKey = "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6";
        String apiSecret = "q1w2e3r4t5y6u7i8o9p0a1s2d3f4g5h6j7k8l9z0x1c2v3b4n5m6q7w8e9r0t1y2u3i4o5p6";
        
        InspirationSquareApiClient client = new InspirationSquareApiClient(baseUrl, apiKey, apiSecret);
        
        try
        {
            // 示例1：获取素材分类列表
            System.out.println("=== 获取素材分类列表 ===");
            ApiResponse<List<InspirationCategory>> categoriesResponse = client.getCategories();
            if (categoriesResponse.isSuccess())
            {
                List<InspirationCategory> categories = categoriesResponse.getData();
                System.out.println("获取到 " + categories.size() + " 个分类：");
                for (InspirationCategory category : categories)
                {
                    System.out.println("- " + category.getCategoryName() + " (ID: " + category.getCategoryId() + 
                                     ", 素材数量: " + category.getMaterialCount() + ")");
                }
            }
            else
            {
                System.err.println("获取分类失败: " + categoriesResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例2：获取素材列表
            System.out.println("=== 获取素材列表 ===");
            ApiResponse<PageResult<InspirationMaterial>> materialsResponse = 
                client.getMaterials(1L, "设计", "IMAGE", 1, 10);
            
            if (materialsResponse.isSuccess())
            {
                PageResult<InspirationMaterial> pageResult = materialsResponse.getData();
                System.out.println("总共找到 " + pageResult.getTotal() + " 个素材：");
                
                for (InspirationMaterial material : pageResult.getRows())
                {
                    System.out.println("- " + material.getTitle() + 
                                     " (类型: " + material.getMaterialType() + 
                                     ", 浏览: " + material.getViewCount() + 
                                     ", 评分: " + material.getRating() + ")");
                }
            }
            else
            {
                System.err.println("获取素材列表失败: " + materialsResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例3：获取素材详情
            System.out.println("=== 获取素材详情 ===");
            ApiResponse<InspirationMaterial> materialResponse = client.getMaterialDetail(1L);
            
            if (materialResponse.isSuccess())
            {
                InspirationMaterial material = materialResponse.getData();
                System.out.println("素材详情：");
                System.out.println("标题: " + material.getTitle());
                System.out.println("描述: " + material.getDescription());
                System.out.println("作者: " + material.getAuthor());
                System.out.println("类型: " + material.getMaterialType());
                System.out.println("是否免费: " + (material.getIsFree() ? "是" : "否"));
                System.out.println("浏览次数: " + material.getViewCount());
                System.out.println("下载次数: " + material.getDownloadCount());
                System.out.println("评分: " + material.getRating());
            }
            else
            {
                System.err.println("获取素材详情失败: " + materialResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例4：搜索素材
            System.out.println("=== 搜索素材 ===");
            InspirationMaterial searchCriteria = new InspirationMaterial();
            searchCriteria.setTitle("创意");
            searchCriteria.setMaterialType("IMAGE");
            searchCriteria.setIsFree(true);
            
            ApiResponse<PageResult<InspirationMaterial>> searchResponse = client.searchMaterials(searchCriteria);
            
            if (searchResponse.isSuccess())
            {
                PageResult<InspirationMaterial> searchResult = searchResponse.getData();
                System.out.println("搜索到 " + searchResult.getTotal() + " 个匹配的素材：");
                
                for (InspirationMaterial material : searchResult.getRows())
                {
                    System.out.println("- " + material.getTitle() + " (评分: " + material.getRating() + ")");
                }
            }
            else
            {
                System.err.println("搜索素材失败: " + searchResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例5：获取热门素材
            System.out.println("=== 获取热门素材 ===");
            ApiResponse<List<InspirationMaterial>> hotResponse = client.getHotMaterials(5);
            
            if (hotResponse.isSuccess())
            {
                List<InspirationMaterial> hotMaterials = hotResponse.getData();
                System.out.println("热门素材 TOP " + hotMaterials.size() + "：");
                
                for (int i = 0; i < hotMaterials.size(); i++)
                {
                    InspirationMaterial material = hotMaterials.get(i);
                    System.out.println((i + 1) + ". " + material.getTitle() + 
                                     " (浏览: " + material.getViewCount() + 
                                     ", 下载: " + material.getDownloadCount() + ")");
                }
            }
            else
            {
                System.err.println("获取热门素材失败: " + hotResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例6：记录素材访问
            System.out.println("=== 记录素材访问 ===");
            ApiResponse<String> visitResponse = client.recordMaterialVisit(1L);
            
            if (visitResponse.isSuccess())
            {
                System.out.println("访问记录成功: " + visitResponse.getData());
            }
            else
            {
                System.err.println("记录访问失败: " + visitResponse.getMsg());
            }
            
            System.out.println();
            
            // 示例7：获取统计信息
            System.out.println("=== 获取统计信息 ===");
            ApiResponse<Object> statsResponse = client.getStatistics();
            
            if (statsResponse.isSuccess())
            {
                System.out.println("统计信息: " + statsResponse.getData());
            }
            else
            {
                System.err.println("获取统计信息失败: " + statsResponse.getMsg());
            }
            
        }
        catch (InspirationSquareApiClient.ApiException e)
        {
            System.err.println("API调用异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
