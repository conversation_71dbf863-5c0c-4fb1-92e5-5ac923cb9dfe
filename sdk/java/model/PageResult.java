package com.example.sdk.model;

import java.util.List;

/**
 * 分页结果封装类
 * 
 * <AUTHOR>
 */
public class PageResult<T>
{
    /** 总记录数 */
    private Long total;

    /** 列表数据 */
    private List<T> rows;

    /** 状态码 */
    private Integer code;

    /** 返回消息 */
    private String msg;

    public PageResult()
    {
    }

    public PageResult(List<T> rows, Long total)
    {
        this.rows = rows;
        this.total = total;
    }

    public Long getTotal()
    {
        return total;
    }

    public void setTotal(Long total)
    {
        this.total = total;
    }

    public List<T> getRows()
    {
        return rows;
    }

    public void setRows(List<T> rows)
    {
        this.rows = rows;
    }

    public Integer getCode()
    {
        return code;
    }

    public void setCode(Integer code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    @Override
    public String toString()
    {
        return "PageResult{" +
                "total=" + total +
                ", rows=" + rows +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
