package com.example.sdk.model;

/**
 * API响应结果封装类
 * 
 * <AUTHOR>
 */
public class ApiResponse<T>
{
    /** 状态码 */
    private Integer code;

    /** 返回消息 */
    private String msg;

    /** 返回数据 */
    private T data;

    /** 时间戳 */
    private Long timestamp;

    public ApiResponse()
    {
    }

    public ApiResponse(Integer code, String msg, T data)
    {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    public Integer getCode()
    {
        return code;
    }

    public void setCode(Integer code)
    {
        this.code = code;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    public Long getTimestamp()
    {
        return timestamp;
    }

    public void setTimestamp(Long timestamp)
    {
        this.timestamp = timestamp;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess()
    {
        return code != null && code == 200;
    }

    @Override
    public String toString()
    {
        return "ApiResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
}
