package com.example.sdk;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

/**
 * 灵感广场API客户端SDK
 * 
 * <AUTHOR>
 */
public class InspirationSquareApiClient
{
    private final String baseUrl;
    private final String apiKey;
    private final String apiSecret;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数
     * 
     * @param baseUrl API基础URL
     * @param apiKey API密钥
     * @param apiSecret API密钥密文
     */
    public InspirationSquareApiClient(String baseUrl, String apiKey, String apiSecret)
    {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 获取素材分类列表
     * 
     * @return 分类列表
     * @throws ApiException API异常
     */
    public ApiResponse<List<InspirationCategory>> getCategories() throws ApiException
    {
        String endpoint = "/external/api/inspiration/categories";
        return get(endpoint, new TypeReference<ApiResponse<List<InspirationCategory>>>() {});
    }

    /**
     * 获取素材列表
     * 
     * @param categoryId 分类ID
     * @param keyword 关键词
     * @param materialType 素材类型
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 素材列表
     * @throws ApiException API异常
     */
    public ApiResponse<PageResult<InspirationMaterial>> getMaterials(Long categoryId, String keyword, 
                                                                    String materialType, Integer pageNum, Integer pageSize) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials";
        Map<String, Object> params = new HashMap<>();
        if (categoryId != null) params.put("categoryId", categoryId);
        if (keyword != null) params.put("keyword", keyword);
        if (materialType != null) params.put("materialType", materialType);
        if (pageNum != null) params.put("pageNum", pageNum);
        if (pageSize != null) params.put("pageSize", pageSize);
        
        return get(endpoint, params, new TypeReference<ApiResponse<PageResult<InspirationMaterial>>>() {});
    }

    /**
     * 获取素材详情
     * 
     * @param materialId 素材ID
     * @return 素材详情
     * @throws ApiException API异常
     */
    public ApiResponse<InspirationMaterial> getMaterialDetail(Long materialId) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials/" + materialId;
        return get(endpoint, new TypeReference<ApiResponse<InspirationMaterial>>() {});
    }

    /**
     * 搜索素材
     * 
     * @param searchCriteria 搜索条件
     * @return 素材列表
     * @throws ApiException API异常
     */
    public ApiResponse<PageResult<InspirationMaterial>> searchMaterials(InspirationMaterial searchCriteria) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials/search";
        return post(endpoint, searchCriteria, new TypeReference<ApiResponse<PageResult<InspirationMaterial>>>() {});
    }

    /**
     * 获取热门素材
     * 
     * @param limit 数量限制
     * @return 热门素材列表
     * @throws ApiException API异常
     */
    public ApiResponse<List<InspirationMaterial>> getHotMaterials(Integer limit) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials/hot";
        Map<String, Object> params = new HashMap<>();
        if (limit != null) params.put("limit", limit);
        
        return get(endpoint, params, new TypeReference<ApiResponse<List<InspirationMaterial>>>() {});
    }

    /**
     * 获取最新素材
     * 
     * @param limit 数量限制
     * @return 最新素材列表
     * @throws ApiException API异常
     */
    public ApiResponse<List<InspirationMaterial>> getLatestMaterials(Integer limit) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials/latest";
        Map<String, Object> params = new HashMap<>();
        if (limit != null) params.put("limit", limit);
        
        return get(endpoint, params, new TypeReference<ApiResponse<List<InspirationMaterial>>>() {});
    }

    /**
     * 记录素材访问
     * 
     * @param materialId 素材ID
     * @return 操作结果
     * @throws ApiException API异常
     */
    public ApiResponse<String> recordMaterialVisit(Long materialId) throws ApiException
    {
        String endpoint = "/external/api/inspiration/materials/" + materialId + "/visit";
        return post(endpoint, null, new TypeReference<ApiResponse<String>>() {});
    }

    /**
     * 获取统计信息
     * 
     * @return 统计信息
     * @throws ApiException API异常
     */
    public ApiResponse<Object> getStatistics() throws ApiException
    {
        String endpoint = "/external/api/inspiration/statistics";
        return get(endpoint, new TypeReference<ApiResponse<Object>>() {});
    }

    /**
     * 执行GET请求
     */
    private <T> T get(String endpoint, TypeReference<T> typeReference) throws ApiException
    {
        return get(endpoint, null, typeReference);
    }

    /**
     * 执行GET请求
     */
    private <T> T get(String endpoint, Map<String, Object> params, TypeReference<T> typeReference) throws ApiException
    {
        try
        {
            String url = baseUrl + endpoint;
            String queryString = buildQueryString(params);
            if (queryString != null && !queryString.isEmpty())
            {
                url += "?" + queryString;
            }

            Request request = new Request.Builder()
                    .url(url)
                    .headers(buildHeaders("GET", endpoint, queryString))
                    .build();

            return executeRequest(request, typeReference);
        }
        catch (Exception e)
        {
            throw new ApiException("GET请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行POST请求
     */
    private <T> T post(String endpoint, Object body, TypeReference<T> typeReference) throws ApiException
    {
        try
        {
            String url = baseUrl + endpoint;
            String jsonBody = body != null ? objectMapper.writeValueAsString(body) : "{}";

            RequestBody requestBody = RequestBody.create(jsonBody, MediaType.get("application/json"));
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .headers(buildHeaders("POST", endpoint, null))
                    .build();

            return executeRequest(request, typeReference);
        }
        catch (Exception e)
        {
            throw new ApiException("POST请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行HTTP请求
     */
    private <T> T executeRequest(Request request, TypeReference<T> typeReference) throws ApiException
    {
        try (Response response = httpClient.newCall(request).execute())
        {
            if (!response.isSuccessful())
            {
                throw new ApiException("HTTP请求失败，状态码: " + response.code());
            }

            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, typeReference);
        }
        catch (IOException e)
        {
            throw new ApiException("解析响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建请求头
     */
    private Headers buildHeaders(String method, String endpoint, String queryString)
    {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signature = generateSignature(method, endpoint, queryString, timestamp);

        return new Headers.Builder()
                .add("X-API-Key", apiKey)
                .add("X-Timestamp", timestamp)
                .add("X-Signature", signature)
                .add("Content-Type", "application/json")
                .build();
    }

    /**
     * 生成请求签名
     */
    private String generateSignature(String method, String endpoint, String queryString, String timestamp)
    {
        try
        {
            StringBuilder signString = new StringBuilder();
            signString.append(method.toUpperCase());
            signString.append(endpoint);
            
            if (queryString != null && !queryString.isEmpty())
            {
                signString.append("?").append(queryString);
            }
            
            signString.append(timestamp);

            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signatureBytes = mac.doFinal(signString.toString().getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : signatureBytes)
            {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1)
                {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        }
        catch (NoSuchAlgorithmException | InvalidKeyException e)
        {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 构建查询字符串
     */
    private String buildQueryString(Map<String, Object> params)
    {
        if (params == null || params.isEmpty())
        {
            return null;
        }

        StringBuilder queryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet())
        {
            if (entry.getValue() != null)
            {
                if (queryString.length() > 0)
                {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }
        }

        return queryString.toString();
    }

    /**
     * API异常类
     */
    public static class ApiException extends Exception
    {
        public ApiException(String message)
        {
            super(message);
        }

        public ApiException(String message, Throwable cause)
        {
            super(message, cause);
        }
    }
}
