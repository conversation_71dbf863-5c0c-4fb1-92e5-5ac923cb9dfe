package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.system.mapper.ExternalSystemMapper;
import com.ruoyi.system.domain.ExternalSystem;
import com.ruoyi.system.service.IExternalSystemService;
import com.ruoyi.system.service.IExternalApiAuthService;

/**
 * 外部系统配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class ExternalSystemServiceImpl implements IExternalSystemService 
{
    @Autowired
    private ExternalSystemMapper externalSystemMapper;

    @Autowired
    private IExternalApiAuthService externalApiAuthService;

    /**
     * 查询外部系统配置
     * 
     * @param systemId 外部系统配置主键
     * @return 外部系统配置
     */
    @Override
    public ExternalSystem selectExternalSystemBySystemId(Long systemId)
    {
        return externalSystemMapper.selectExternalSystemBySystemId(systemId);
    }

    /**
     * 根据API密钥查询外部系统配置
     * 
     * @param apiKey API密钥
     * @return 外部系统配置
     */
    @Override
    public ExternalSystem selectExternalSystemByApiKey(String apiKey)
    {
        return externalSystemMapper.selectExternalSystemByApiKey(apiKey);
    }

    /**
     * 根据系统编码查询外部系统配置
     * 
     * @param systemCode 系统编码
     * @return 外部系统配置
     */
    @Override
    public ExternalSystem selectExternalSystemBySystemCode(String systemCode)
    {
        return externalSystemMapper.selectExternalSystemBySystemCode(systemCode);
    }

    /**
     * 查询外部系统配置列表
     * 
     * @param externalSystem 外部系统配置
     * @return 外部系统配置
     */
    @Override
    public List<ExternalSystem> selectExternalSystemList(ExternalSystem externalSystem)
    {
        return externalSystemMapper.selectExternalSystemList(externalSystem);
    }

    /**
     * 新增外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    @Override
    public int insertExternalSystem(ExternalSystem externalSystem)
    {
        // 生成API密钥和密文
        String apiKey = externalApiAuthService.generateApiKey(externalSystem.getSystemCode());
        String apiSecret = externalApiAuthService.generateApiSecret(externalSystem.getSystemCode());
        
        externalSystem.setApiKey(apiKey);
        externalSystem.setApiSecret(apiSecret);
        externalSystem.setCreateTime(DateUtils.getNowDate());
        
        return externalSystemMapper.insertExternalSystem(externalSystem);
    }

    /**
     * 修改外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    @Override
    public int updateExternalSystem(ExternalSystem externalSystem)
    {
        externalSystem.setUpdateTime(DateUtils.getNowDate());
        
        // 清除缓存
        ExternalSystem oldSystem = externalSystemMapper.selectExternalSystemBySystemId(externalSystem.getSystemId());
        if (oldSystem != null)
        {
            externalApiAuthService.clearSystemConfigCache(oldSystem.getApiKey());
            externalApiAuthService.clearRateLimitCache(oldSystem.getSystemCode());
        }
        
        return externalSystemMapper.updateExternalSystem(externalSystem);
    }

    /**
     * 批量删除外部系统配置
     * 
     * @param systemIds 需要删除的外部系统配置主键
     * @return 结果
     */
    @Override
    public int deleteExternalSystemBySystemIds(Long[] systemIds)
    {
        // 清除相关缓存
        for (Long systemId : systemIds)
        {
            ExternalSystem system = externalSystemMapper.selectExternalSystemBySystemId(systemId);
            if (system != null)
            {
                externalApiAuthService.clearSystemConfigCache(system.getApiKey());
                externalApiAuthService.clearRateLimitCache(system.getSystemCode());
            }
        }
        
        return externalSystemMapper.deleteExternalSystemBySystemIds(systemIds);
    }

    /**
     * 删除外部系统配置信息
     * 
     * @param systemId 外部系统配置主键
     * @return 结果
     */
    @Override
    public int deleteExternalSystemBySystemId(Long systemId)
    {
        // 清除相关缓存
        ExternalSystem system = externalSystemMapper.selectExternalSystemBySystemId(systemId);
        if (system != null)
        {
            externalApiAuthService.clearSystemConfigCache(system.getApiKey());
            externalApiAuthService.clearRateLimitCache(system.getSystemCode());
        }
        
        return externalSystemMapper.deleteExternalSystemBySystemId(systemId);
    }

    /**
     * 校验系统编码是否唯一
     * 
     * @param externalSystem 外部系统配置信息
     * @return 结果
     */
    @Override
    public boolean checkSystemCodeUnique(ExternalSystem externalSystem)
    {
        Long systemId = StringUtils.isNull(externalSystem.getSystemId()) ? -1L : externalSystem.getSystemId();
        ExternalSystem info = externalSystemMapper.checkSystemCodeUnique(externalSystem.getSystemCode());
        if (StringUtils.isNotNull(info) && info.getSystemId().longValue() != systemId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验系统名称是否唯一
     * 
     * @param externalSystem 外部系统配置信息
     * @return 结果
     */
    @Override
    public boolean checkSystemNameUnique(ExternalSystem externalSystem)
    {
        Long systemId = StringUtils.isNull(externalSystem.getSystemId()) ? -1L : externalSystem.getSystemId();
        ExternalSystem info = externalSystemMapper.checkSystemNameUnique(externalSystem.getSystemName());
        if (StringUtils.isNotNull(info) && info.getSystemId().longValue() != systemId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 重新生成API密钥
     * 
     * @param systemId 系统ID
     * @return 结果
     */
    @Override
    public int regenerateApiKey(Long systemId)
    {
        ExternalSystem externalSystem = externalSystemMapper.selectExternalSystemBySystemId(systemId);
        if (externalSystem == null)
        {
            return 0;
        }

        // 清除旧的缓存
        externalApiAuthService.clearSystemConfigCache(externalSystem.getApiKey());
        externalApiAuthService.clearRateLimitCache(externalSystem.getSystemCode());

        // 生成新的API密钥和密文
        String newApiKey = externalApiAuthService.generateApiKey(externalSystem.getSystemCode());
        String newApiSecret = externalApiAuthService.generateApiSecret(externalSystem.getSystemCode());

        externalSystem.setApiKey(newApiKey);
        externalSystem.setApiSecret(newApiSecret);
        externalSystem.setUpdateTime(DateUtils.getNowDate());

        return externalSystemMapper.updateExternalSystem(externalSystem);
    }

    /**
     * 启用/禁用外部系统
     * 
     * @param systemId 系统ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int changeStatus(Long systemId, String status)
    {
        ExternalSystem externalSystem = new ExternalSystem();
        externalSystem.setSystemId(systemId);
        externalSystem.setStatus(status);
        externalSystem.setUpdateTime(DateUtils.getNowDate());

        // 如果禁用系统，清除相关缓存
        if ("1".equals(status))
        {
            ExternalSystem system = externalSystemMapper.selectExternalSystemBySystemId(systemId);
            if (system != null)
            {
                externalApiAuthService.clearSystemConfigCache(system.getApiKey());
                externalApiAuthService.clearRateLimitCache(system.getSystemCode());
            }
        }

        return externalSystemMapper.updateExternalSystem(externalSystem);
    }
}
