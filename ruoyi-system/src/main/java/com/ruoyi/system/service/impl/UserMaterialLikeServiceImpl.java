package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.UserMaterialLikeMapper;
import com.ruoyi.system.domain.UserMaterialLike;
import com.ruoyi.system.service.IUserMaterialLikeService;

/**
 * 用户素材点赞Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Service
public class UserMaterialLikeServiceImpl implements IUserMaterialLikeService 
{
    @Autowired
    private UserMaterialLikeMapper userMaterialLikeMapper;

    /**
     * 查询用户素材点赞
     * 
     * @param likeId 用户素材点赞主键
     * @return 用户素材点赞
     */
    @Override
    public UserMaterialLike selectUserMaterialLikeByLikeId(Long likeId)
    {
        return userMaterialLikeMapper.selectUserMaterialLikeByLikeId(likeId);
    }

    /**
     * 查询用户素材点赞列表
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 用户素材点赞
     */
    @Override
    public List<UserMaterialLike> selectUserMaterialLikeList(UserMaterialLike userMaterialLike)
    {
        return userMaterialLikeMapper.selectUserMaterialLikeList(userMaterialLike);
    }

    /**
     * 新增用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    @Override
    public int insertUserMaterialLike(UserMaterialLike userMaterialLike)
    {
        return userMaterialLikeMapper.insertUserMaterialLike(userMaterialLike);
    }

    /**
     * 修改用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    @Override
    public int updateUserMaterialLike(UserMaterialLike userMaterialLike)
    {
        return userMaterialLikeMapper.updateUserMaterialLike(userMaterialLike);
    }

    /**
     * 批量删除用户素材点赞
     * 
     * @param likeIds 需要删除的用户素材点赞主键
     * @return 结果
     */
    @Override
    public int deleteUserMaterialLikeByLikeIds(Long[] likeIds)
    {
        return userMaterialLikeMapper.deleteUserMaterialLikeByLikeIds(likeIds);
    }

    /**
     * 删除用户素材点赞信息
     * 
     * @param likeId 用户素材点赞主键
     * @return 结果
     */
    @Override
    public int deleteUserMaterialLikeByLikeId(Long likeId)
    {
        return userMaterialLikeMapper.deleteUserMaterialLikeByLikeId(likeId);
    }

    /**
     * 点赞/取消点赞素材
     */
    @Override
    public Map<String, Object> toggleLike(Long userId, String materialId, String action, 
                                        String materialTitle, String materialTag, 
                                        String photoUrl, String detailUrl)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 检查是否已点赞
        boolean isLiked = isLiked(userId, materialId);
        
        if ("like".equals(action) && !isLiked)
        {
            // 执行点赞
            UserMaterialLike like = new UserMaterialLike();
            like.setUserId(userId);
            like.setMaterialId(materialId);
            like.setMaterialTitle(materialTitle);
            like.setMaterialTag(materialTag);
            like.setPhotoUrl(photoUrl);
            like.setDetailUrl(detailUrl);
            like.setLikeTime(new Date());
            like.setCreateBy(SecurityUtils.getUsername());
            like.setCreateTime(new Date());
            
            userMaterialLikeMapper.insertUserMaterialLike(like);
            
            result.put("materialId", materialId);
            result.put("isLiked", true);
            result.put("likeCount", getLikeCount(materialId));
        }
        else if ("unlike".equals(action) && isLiked)
        {
            // 取消点赞
            userMaterialLikeMapper.deleteByUserIdAndMaterialId(userId, materialId);
            
            result.put("materialId", materialId);
            result.put("isLiked", false);
            result.put("likeCount", getLikeCount(materialId));
        }
        else
        {
            // 状态未改变
            result.put("materialId", materialId);
            result.put("isLiked", isLiked);
            result.put("likeCount", getLikeCount(materialId));
        }
        
        return result;
    }

    /**
     * 检查用户是否点赞了指定素材
     */
    @Override
    public boolean isLiked(Long userId, String materialId)
    {
        UserMaterialLike query = new UserMaterialLike();
        query.setUserId(userId);
        query.setMaterialId(materialId);
        
        List<UserMaterialLike> likes = userMaterialLikeMapper.selectUserMaterialLikeList(query);
        return !likes.isEmpty();
    }

    /**
     * 批量检查用户点赞状态
     */
    @Override
    public Map<String, Boolean> batchCheckLikeStatus(Long userId, List<String> materialIds)
    {
        Map<String, Boolean> result = new HashMap<>();
        
        // 查询用户对这些素材的点赞记录
        List<String> likedMaterialIds = userMaterialLikeMapper.selectLikedMaterialIds(userId, materialIds);
        
        // 构建结果Map
        for (String materialId : materialIds)
        {
            result.put(materialId, likedMaterialIds.contains(materialId));
        }
        
        return result;
    }

    /**
     * 获取素材点赞数
     */
    @Override
    public int getLikeCount(String materialId)
    {
        return userMaterialLikeMapper.countLikesByMaterialId(materialId);
    }

    /**
     * 查询用户的喜欢列表
     */
    @Override
    public List<UserMaterialLike> selectUserLikes(Long userId, String materialTag)
    {
        UserMaterialLike query = new UserMaterialLike();
        query.setUserId(userId);
        query.setMaterialTag(materialTag);
        
        return userMaterialLikeMapper.selectUserMaterialLikeList(query);
    }
}
