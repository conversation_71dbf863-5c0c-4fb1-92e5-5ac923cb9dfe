package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.UserMaterialLike;

/**
 * 用户素材点赞Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IUserMaterialLikeService 
{
    /**
     * 查询用户素材点赞
     * 
     * @param likeId 用户素材点赞主键
     * @return 用户素材点赞
     */
    public UserMaterialLike selectUserMaterialLikeByLikeId(Long likeId);

    /**
     * 查询用户素材点赞列表
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 用户素材点赞集合
     */
    public List<UserMaterialLike> selectUserMaterialLikeList(UserMaterialLike userMaterialLike);

    /**
     * 新增用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    public int insertUserMaterialLike(UserMaterialLike userMaterialLike);

    /**
     * 修改用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    public int updateUserMaterialLike(UserMaterialLike userMaterialLike);

    /**
     * 批量删除用户素材点赞
     * 
     * @param likeIds 需要删除的用户素材点赞主键集合
     * @return 结果
     */
    public int deleteUserMaterialLikeByLikeIds(Long[] likeIds);

    /**
     * 删除用户素材点赞信息
     * 
     * @param likeId 用户素材点赞主键
     * @return 结果
     */
    public int deleteUserMaterialLikeByLikeId(Long likeId);

    /**
     * 点赞/取消点赞素材
     * 
     * @param userId 用户ID
     * @param materialId 素材ID
     * @param action 操作类型 like/unlike
     * @param materialTitle 素材标题
     * @param materialTag 素材标签
     * @param photoUrl 缩略图URL
     * @param detailUrl 详情图URL
     * @return 操作结果
     */
    public Map<String, Object> toggleLike(Long userId, String materialId, String action, 
                                        String materialTitle, String materialTag, 
                                        String photoUrl, String detailUrl);

    /**
     * 检查用户是否点赞了指定素材
     * 
     * @param userId 用户ID
     * @param materialId 素材ID
     * @return 是否点赞
     */
    public boolean isLiked(Long userId, String materialId);

    /**
     * 批量检查用户点赞状态
     * 
     * @param userId 用户ID
     * @param materialIds 素材ID列表
     * @return 点赞状态Map
     */
    public Map<String, Boolean> batchCheckLikeStatus(Long userId, List<String> materialIds);

    /**
     * 获取素材点赞数
     * 
     * @param materialId 素材ID
     * @return 点赞数
     */
    public int getLikeCount(String materialId);

    /**
     * 查询用户的喜欢列表
     * 
     * @param userId 用户ID
     * @param materialTag 素材标签（可选）
     * @return 喜欢的素材列表
     */
    public List<UserMaterialLike> selectUserLikes(Long userId, String materialTag);
}
