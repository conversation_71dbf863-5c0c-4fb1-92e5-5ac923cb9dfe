package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExternalApiLogMapper;
import com.ruoyi.system.domain.ExternalApiLog;
import com.ruoyi.system.service.IExternalApiLogService;

/**
 * 外部API调用日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class ExternalApiLogServiceImpl implements IExternalApiLogService 
{
    @Autowired
    private ExternalApiLogMapper externalApiLogMapper;

    /**
     * 查询外部API调用日志
     * 
     * @param logId 外部API调用日志主键
     * @return 外部API调用日志
     */
    @Override
    public ExternalApiLog selectExternalApiLogByLogId(Long logId)
    {
        return externalApiLogMapper.selectExternalApiLogByLogId(logId);
    }

    /**
     * 查询外部API调用日志列表
     * 
     * @param externalApiLog 外部API调用日志
     * @return 外部API调用日志
     */
    @Override
    public List<ExternalApiLog> selectExternalApiLogList(ExternalApiLog externalApiLog)
    {
        return externalApiLogMapper.selectExternalApiLogList(externalApiLog);
    }

    /**
     * 新增外部API调用日志
     * 
     * @param externalApiLog 外部API调用日志
     * @return 结果
     */
    @Override
    public int insertExternalApiLog(ExternalApiLog externalApiLog)
    {
        return externalApiLogMapper.insertExternalApiLog(externalApiLog);
    }

    /**
     * 修改外部API调用日志
     * 
     * @param externalApiLog 外部API调用日志
     * @return 结果
     */
    @Override
    public int updateExternalApiLog(ExternalApiLog externalApiLog)
    {
        return externalApiLogMapper.updateExternalApiLog(externalApiLog);
    }

    /**
     * 批量删除外部API调用日志
     * 
     * @param logIds 需要删除的外部API调用日志主键
     * @return 结果
     */
    @Override
    public int deleteExternalApiLogByLogIds(Long[] logIds)
    {
        return externalApiLogMapper.deleteExternalApiLogByLogIds(logIds);
    }

    /**
     * 删除外部API调用日志信息
     * 
     * @param logId 外部API调用日志主键
     * @return 结果
     */
    @Override
    public int deleteExternalApiLogByLogId(Long logId)
    {
        return externalApiLogMapper.deleteExternalApiLogByLogId(logId);
    }

    /**
     * 清理指定天数前的日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    @Override
    public int cleanExternalApiLogByDays(int days)
    {
        return externalApiLogMapper.cleanExternalApiLogByDays(days);
    }

    /**
     * 统计API调用次数
     * 
     * @param externalApiLog 查询条件
     * @return 调用次数
     */
    @Override
    public Long countExternalApiLog(ExternalApiLog externalApiLog)
    {
        return externalApiLogMapper.countExternalApiLog(externalApiLog);
    }

    /**
     * 统计API调用成功率
     * 
     * @param systemId 系统ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率统计
     */
    @Override
    public List<ExternalApiLog> selectApiSuccessRate(Long systemId, String startTime, String endTime)
    {
        return externalApiLogMapper.selectApiSuccessRate(systemId, startTime, endTime);
    }
}
