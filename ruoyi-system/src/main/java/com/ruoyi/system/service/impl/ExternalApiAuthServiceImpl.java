package com.ruoyi.system.service.impl;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.concurrent.TimeUnit;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.ExternalSystem;
import com.ruoyi.system.service.IExternalSystemService;
import com.ruoyi.system.service.IExternalApiAuthService;

/**
 * 外部API认证服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class ExternalApiAuthServiceImpl implements IExternalApiAuthService
{
    private static final Logger log = LoggerFactory.getLogger(ExternalApiAuthServiceImpl.class);

    @Autowired
    private IExternalSystemService externalSystemService;

    @Autowired
    private RedisCache redisCache;

    /** 限流缓存键前缀 */
    private static final String RATE_LIMIT_KEY = "external_api_rate_limit:";

    /** 系统配置缓存键前缀 */
    private static final String SYSTEM_CONFIG_KEY = "external_system_config:";

    /** 签名有效期（秒） */
    private static final long SIGNATURE_VALID_SECONDS = 300;

    /**
     * 验证API密钥
     */
    @Override
    public ExternalSystem validateApiKey(String apiKey)
    {
        if (StringUtils.isEmpty(apiKey))
        {
            return null;
        }

        // 先从缓存中获取
        String cacheKey = SYSTEM_CONFIG_KEY + apiKey;
        ExternalSystem cachedSystem = redisCache.getCacheObject(cacheKey);
        if (cachedSystem != null)
        {
            return cachedSystem;
        }

        // 从数据库查询
        ExternalSystem externalSystem = externalSystemService.selectExternalSystemByApiKey(apiKey);
        if (externalSystem != null && "0".equals(externalSystem.getStatus()))
        {
            // 缓存5分钟
            redisCache.setCacheObject(cacheKey, externalSystem, 5, TimeUnit.MINUTES);
            return externalSystem;
        }

        return null;
    }

    /**
     * 验证请求签名
     */
    @Override
    public boolean validateSignature(HttpServletRequest request, String apiSecret, String signature, String timestamp)
    {
        try
        {
            // 验证时间戳
            if (StringUtils.isEmpty(timestamp))
            {
                log.warn("请求缺少时间戳");
                return false;
            }

            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis() / 1000;
            
            if (Math.abs(currentTime - requestTime) > SIGNATURE_VALID_SECONDS)
            {
                log.warn("请求时间戳过期，请求时间: {}, 当前时间: {}", requestTime, currentTime);
                return false;
            }

            // 验证签名
            if (StringUtils.isEmpty(signature))
            {
                log.warn("请求缺少签名");
                return false;
            }

            String expectedSignature = generateSignature(request, apiSecret, timestamp);
            boolean isValid = signature.equals(expectedSignature);
            
            if (!isValid)
            {
                log.warn("签名验证失败，期望: {}, 实际: {}", expectedSignature, signature);
            }
            
            return isValid;
        }
        catch (Exception e)
        {
            log.error("签名验证过程中发生异常", e);
            return false;
        }
    }

    /**
     * 生成请求签名
     */
    @Override
    public String generateSignature(HttpServletRequest request, String apiSecret, String timestamp)
    {
        try
        {
            // 构建签名字符串：HTTP方法 + URI + 查询参数 + 时间戳
            StringBuilder signString = new StringBuilder();
            signString.append(request.getMethod().toUpperCase());
            signString.append(request.getRequestURI());
            
            String queryString = request.getQueryString();
            if (StringUtils.isNotEmpty(queryString))
            {
                signString.append("?").append(queryString);
            }
            
            signString.append(timestamp);

            // 使用HMAC-SHA256生成签名
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signatureBytes = mac.doFinal(signString.toString().getBytes(StandardCharsets.UTF_8));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : signatureBytes)
            {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1)
                {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        }
        catch (Exception e)
        {
            log.error("生成签名时发生异常", e);
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 检查限流
     */
    @Override
    public boolean checkRateLimit(String systemCode, Integer rateLimit)
    {
        if (rateLimit == null || rateLimit <= 0)
        {
            return true; // 没有配置限流，允许通过
        }

        String rateLimitKey = RATE_LIMIT_KEY + systemCode;
        
        // 获取当前计数
        Integer currentCount = redisCache.getCacheObject(rateLimitKey);
        if (currentCount == null)
        {
            currentCount = 0;
        }

        if (currentCount >= rateLimit)
        {
            log.warn("系统 {} 请求频率超过限制，当前: {}, 限制: {}", systemCode, currentCount, rateLimit);
            return false;
        }

        // 增加计数
        currentCount++;
        redisCache.setCacheObject(rateLimitKey, currentCount, 1, TimeUnit.MINUTES);
        
        return true;
    }

    /**
     * 生成API密钥
     */
    @Override
    public String generateApiKey(String systemCode)
    {
        try
        {
            String source = systemCode + System.currentTimeMillis();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(source.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash)
            {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1)
                {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString().substring(0, 32); // 取前32位作为API Key
        }
        catch (Exception e)
        {
            log.error("生成API密钥时发生异常", e);
            throw new RuntimeException("API密钥生成失败", e);
        }
    }

    /**
     * 生成API密钥密文
     */
    @Override
    public String generateApiSecret(String systemCode)
    {
        try
        {
            String source = systemCode + "SECRET" + System.currentTimeMillis();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(source.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash)
            {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1)
                {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString(); // 完整的64位作为Secret
        }
        catch (Exception e)
        {
            log.error("生成API密钥密文时发生异常", e);
            throw new RuntimeException("API密钥密文生成失败", e);
        }
    }

    /**
     * 清除系统配置缓存
     */
    @Override
    public void clearSystemConfigCache(String apiKey)
    {
        String cacheKey = SYSTEM_CONFIG_KEY + apiKey;
        redisCache.deleteObject(cacheKey);
    }

    /**
     * 清除限流缓存
     */
    @Override
    public void clearRateLimitCache(String systemCode)
    {
        String rateLimitKey = RATE_LIMIT_KEY + systemCode;
        redisCache.deleteObject(rateLimitKey);
    }
}
