package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.HelpCategory;
import com.ruoyi.system.domain.HelpDocument;

/**
 * 帮助文档Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IHelpService 
{
    // ==================== 分类相关 ====================
    
    /**
     * 查询帮助文档分类
     * 
     * @param id 帮助文档分类主键
     * @return 帮助文档分类
     */
    public HelpCategory selectHelpCategoryById(Long id);

    /**
     * 查询帮助文档分类列表
     * 
     * @param helpCategory 帮助文档分类
     * @return 帮助文档分类集合
     */
    public List<HelpCategory> selectHelpCategoryList(HelpCategory helpCategory);

    /**
     * 新增帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    public int insertHelpCategory(HelpCategory helpCategory);

    /**
     * 修改帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    public int updateHelpCategory(HelpCategory helpCategory);

    /**
     * 批量删除帮助文档分类
     * 
     * @param ids 需要删除的帮助文档分类主键集合
     * @return 结果
     */
    public int deleteHelpCategoryByIds(Long[] ids);

    /**
     * 删除帮助文档分类信息
     * 
     * @param id 帮助文档分类主键
     * @return 结果
     */
    public int deleteHelpCategoryById(Long id);

    // ==================== 文档相关 ====================
    
    /**
     * 查询帮助文档
     * 
     * @param id 帮助文档主键
     * @return 帮助文档
     */
    public HelpDocument selectHelpDocumentById(Long id);

    /**
     * 查询帮助文档列表
     * 
     * @param helpDocument 帮助文档
     * @return 帮助文档集合
     */
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument);

    /**
     * 新增帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int insertHelpDocument(HelpDocument helpDocument);

    /**
     * 修改帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int updateHelpDocument(HelpDocument helpDocument);

    /**
     * 批量删除帮助文档
     * 
     * @param ids 需要删除的帮助文档主键集合
     * @return 结果
     */
    public int deleteHelpDocumentByIds(Long[] ids);

    /**
     * 删除帮助文档信息
     * 
     * @param id 帮助文档主键
     * @return 结果
     */
    public int deleteHelpDocumentById(Long id);

    // ==================== 业务相关 ====================
    
    /**
     * 获取按分类分组的文档列表
     * 
     * @param categoryId 分类ID（可选）
     * @param keyword 搜索关键词（可选）
     * @return 分组后的文档数据
     */
    public Map<String, Object> getDocumentsGroupByCategory(Long categoryId, String keyword);

    /**
     * 搜索帮助文档
     * 
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    public List<HelpDocument> searchDocuments(String keyword);

    /**
     * 获取热门文档
     * 
     * @param limit 数量限制
     * @return 热门文档列表
     */
    public List<HelpDocument> getHotDocuments(Integer limit);

    /**
     * 增加文档查看次数
     *
     * @param id 文档ID
     * @return 结果
     */
    public int incrementViewCount(Long id);

    /**
     * 获取常见问题（FAQ）
     *
     * @param limit 数量限制
     * @return FAQ列表
     */
    public List<HelpDocument> getFaqDocuments(Integer limit);
}
