package com.ruoyi.system.service;

import javax.servlet.http.HttpServletRequest;
import com.ruoyi.system.domain.ExternalSystem;

/**
 * 外部API认证服务接口
 * 
 * <AUTHOR>
 */
public interface IExternalApiAuthService
{
    /**
     * 验证API密钥
     * 
     * @param apiKey API密钥
     * @return 外部系统配置
     */
    ExternalSystem validateApiKey(String apiKey);

    /**
     * 验证请求签名
     * 
     * @param request HTTP请求
     * @param apiSecret API密钥密文
     * @param signature 请求签名
     * @param timestamp 时间戳
     * @return 是否验证通过
     */
    boolean validateSignature(HttpServletRequest request, String apiSecret, String signature, String timestamp);

    /**
     * 生成请求签名
     * 
     * @param request HTTP请求
     * @param apiSecret API密钥密文
     * @param timestamp 时间戳
     * @return 签名字符串
     */
    String generateSignature(HttpServletRequest request, String apiSecret, String timestamp);

    /**
     * 检查限流
     * 
     * @param systemCode 系统编码
     * @param rateLimit 限流配置
     * @return 是否通过限流检查
     */
    boolean checkRateLimit(String systemCode, Integer rateLimit);

    /**
     * 生成API密钥
     * 
     * @param systemCode 系统编码
     * @return API密钥
     */
    String generateApiKey(String systemCode);

    /**
     * 生成API密钥密文
     * 
     * @param systemCode 系统编码
     * @return API密钥密文
     */
    String generateApiSecret(String systemCode);

    /**
     * 清除系统配置缓存
     * 
     * @param apiKey API密钥
     */
    void clearSystemConfigCache(String apiKey);

    /**
     * 清除限流缓存
     * 
     * @param systemCode 系统编码
     */
    void clearRateLimitCache(String systemCode);
}
