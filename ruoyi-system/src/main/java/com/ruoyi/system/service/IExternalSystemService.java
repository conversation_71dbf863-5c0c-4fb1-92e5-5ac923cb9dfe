package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ExternalSystem;

/**
 * 外部系统配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface IExternalSystemService 
{
    /**
     * 查询外部系统配置
     * 
     * @param systemId 外部系统配置主键
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemBySystemId(Long systemId);

    /**
     * 根据API密钥查询外部系统配置
     * 
     * @param apiKey API密钥
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemByApiKey(String apiKey);

    /**
     * 根据系统编码查询外部系统配置
     * 
     * @param systemCode 系统编码
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemBySystemCode(String systemCode);

    /**
     * 查询外部系统配置列表
     * 
     * @param externalSystem 外部系统配置
     * @return 外部系统配置集合
     */
    public List<ExternalSystem> selectExternalSystemList(ExternalSystem externalSystem);

    /**
     * 新增外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    public int insertExternalSystem(ExternalSystem externalSystem);

    /**
     * 修改外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    public int updateExternalSystem(ExternalSystem externalSystem);

    /**
     * 批量删除外部系统配置
     * 
     * @param systemIds 需要删除的外部系统配置主键集合
     * @return 结果
     */
    public int deleteExternalSystemBySystemIds(Long[] systemIds);

    /**
     * 删除外部系统配置信息
     * 
     * @param systemId 外部系统配置主键
     * @return 结果
     */
    public int deleteExternalSystemBySystemId(Long systemId);

    /**
     * 校验系统编码是否唯一
     * 
     * @param externalSystem 外部系统配置信息
     * @return 结果
     */
    public boolean checkSystemCodeUnique(ExternalSystem externalSystem);

    /**
     * 校验系统名称是否唯一
     * 
     * @param externalSystem 外部系统配置信息
     * @return 结果
     */
    public boolean checkSystemNameUnique(ExternalSystem externalSystem);

    /**
     * 重新生成API密钥
     * 
     * @param systemId 系统ID
     * @return 结果
     */
    public int regenerateApiKey(Long systemId);

    /**
     * 启用/禁用外部系统
     * 
     * @param systemId 系统ID
     * @param status 状态
     * @return 结果
     */
    public int changeStatus(Long systemId, String status);
}
