package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HelpCategoryMapper;
import com.ruoyi.system.mapper.HelpDocumentMapper;
import com.ruoyi.system.domain.HelpCategory;
import com.ruoyi.system.domain.HelpDocument;
import com.ruoyi.system.service.IHelpService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 帮助文档Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class HelpServiceImpl implements IHelpService 
{
    @Autowired
    private HelpCategoryMapper helpCategoryMapper;

    @Autowired
    private HelpDocumentMapper helpDocumentMapper;

    // ==================== 分类相关 ====================
    
    /**
     * 查询帮助文档分类
     * 
     * @param id 帮助文档分类主键
     * @return 帮助文档分类
     */
    @Override
    public HelpCategory selectHelpCategoryById(Long id)
    {
        return helpCategoryMapper.selectHelpCategoryById(id);
    }

    /**
     * 查询帮助文档分类列表
     * 
     * @param helpCategory 帮助文档分类
     * @return 帮助文档分类
     */
    @Override
    public List<HelpCategory> selectHelpCategoryList(HelpCategory helpCategory)
    {
        return helpCategoryMapper.selectHelpCategoryList(helpCategory);
    }

    /**
     * 新增帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    @Override
    public int insertHelpCategory(HelpCategory helpCategory)
    {
        helpCategory.setCreateBy(SecurityUtils.getUsername());
        helpCategory.setCreateTime(DateUtils.getNowDate());
        return helpCategoryMapper.insertHelpCategory(helpCategory);
    }

    /**
     * 修改帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    @Override
    public int updateHelpCategory(HelpCategory helpCategory)
    {
        helpCategory.setUpdateBy(SecurityUtils.getUsername());
        helpCategory.setUpdateTime(DateUtils.getNowDate());
        return helpCategoryMapper.updateHelpCategory(helpCategory);
    }

    /**
     * 批量删除帮助文档分类
     * 
     * @param ids 需要删除的帮助文档分类主键
     * @return 结果
     */
    @Override
    public int deleteHelpCategoryByIds(Long[] ids)
    {
        return helpCategoryMapper.deleteHelpCategoryByIds(ids);
    }

    /**
     * 删除帮助文档分类信息
     * 
     * @param id 帮助文档分类主键
     * @return 结果
     */
    @Override
    public int deleteHelpCategoryById(Long id)
    {
        return helpCategoryMapper.deleteHelpCategoryById(id);
    }

    // ==================== 文档相关 ====================
    
    /**
     * 查询帮助文档
     * 
     * @param id 帮助文档主键
     * @return 帮助文档
     */
    @Override
    public HelpDocument selectHelpDocumentById(Long id)
    {
        return helpDocumentMapper.selectHelpDocumentById(id);
    }

    /**
     * 查询帮助文档列表
     * 
     * @param helpDocument 帮助文档
     * @return 帮助文档
     */
    @Override
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument)
    {
        return helpDocumentMapper.selectHelpDocumentList(helpDocument);
    }

    /**
     * 新增帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    @Override
    public int insertHelpDocument(HelpDocument helpDocument)
    {
        helpDocument.setCreateBy(SecurityUtils.getUsername());
        helpDocument.setCreateTime(DateUtils.getNowDate());
        return helpDocumentMapper.insertHelpDocument(helpDocument);
    }

    /**
     * 修改帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    @Override
    public int updateHelpDocument(HelpDocument helpDocument)
    {
        helpDocument.setUpdateBy(SecurityUtils.getUsername());
        helpDocument.setUpdateTime(DateUtils.getNowDate());
        return helpDocumentMapper.updateHelpDocument(helpDocument);
    }

    /**
     * 批量删除帮助文档
     * 
     * @param ids 需要删除的帮助文档主键
     * @return 结果
     */
    @Override
    public int deleteHelpDocumentByIds(Long[] ids)
    {
        return helpDocumentMapper.deleteHelpDocumentByIds(ids);
    }

    /**
     * 删除帮助文档信息
     * 
     * @param id 帮助文档主键
     * @return 结果
     */
    @Override
    public int deleteHelpDocumentById(Long id)
    {
        return helpDocumentMapper.deleteHelpDocumentById(id);
    }

    // ==================== 业务相关 ====================
    
    /**
     * 获取按分类分组的文档列表
     * 
     * @param categoryId 分类ID（可选）
     * @param keyword 搜索关键词（可选）
     * @return 分组后的文档数据
     */
    @Override
    public Map<String, Object> getDocumentsGroupByCategory(Long categoryId, String keyword)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取分类列表
        HelpCategory categoryQuery = new HelpCategory();
        categoryQuery.setStatus("0"); // 只查询正常状态的分类
        List<HelpCategory> categories = helpCategoryMapper.selectHelpCategoryList(categoryQuery);
        
        // 获取文档列表
        HelpDocument documentQuery = new HelpDocument();
        documentQuery.setStatus("0"); // 只查询正常状态的文档
        if (categoryId != null) {
            documentQuery.setCategoryId(categoryId);
        }
        List<HelpDocument> allDocuments = helpDocumentMapper.selectHelpDocumentList(documentQuery);
        
        // 如果有搜索关键词，进行过滤
        if (keyword != null && !keyword.trim().isEmpty()) {
            allDocuments = allDocuments.stream()
                .filter(doc -> doc.getTitle().contains(keyword) || 
                              (doc.getKeywords() != null && doc.getKeywords().contains(keyword)) ||
                              (doc.getSummary() != null && doc.getSummary().contains(keyword)))
                .collect(Collectors.toList());
        }
        
        // 按分类分组
        List<Map<String, Object>> categoryList = new ArrayList<>();
        for (HelpCategory category : categories) {
            Map<String, Object> categoryMap = new HashMap<>();
            categoryMap.put("id", category.getId());
            categoryMap.put("name", category.getName());
            categoryMap.put("icon", category.getIcon());
            
            // 获取该分类下的文档
            List<HelpDocument> categoryDocuments = allDocuments.stream()
                .filter(doc -> category.getId().equals(doc.getCategoryId()))
                .collect(Collectors.toList());
            
            categoryMap.put("documents", categoryDocuments);
            categoryList.add(categoryMap);
        }
        
        result.put("categories", categoryList);
        
        // 获取热门文档
        List<HelpDocument> hotDocuments = getHotDocuments(5);
        result.put("hotDocuments", hotDocuments);
        
        return result;
    }

    /**
     * 搜索帮助文档
     * 
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    @Override
    public List<HelpDocument> searchDocuments(String keyword)
    {
        return helpDocumentMapper.searchDocuments(keyword);
    }

    /**
     * 获取热门文档
     * 
     * @param limit 数量限制
     * @return 热门文档列表
     */
    @Override
    public List<HelpDocument> getHotDocuments(Integer limit)
    {
        return helpDocumentMapper.getHotDocuments(limit);
    }

    /**
     * 增加文档查看次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long id)
    {
        return helpDocumentMapper.incrementViewCount(id);
    }

    /**
     * 获取常见问题（FAQ）
     * 
     * @param limit 数量限制
     * @return FAQ列表
     */
    @Override
    public List<HelpDocument> getFaqDocuments(Integer limit)
    {
        return helpDocumentMapper.getFaqDocuments(limit);
    }
}
