package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.UserMaterialLike;

/**
 * 用户素材点赞Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface UserMaterialLikeMapper 
{
    /**
     * 查询用户素材点赞
     * 
     * @param likeId 用户素材点赞主键
     * @return 用户素材点赞
     */
    public UserMaterialLike selectUserMaterialLikeByLikeId(Long likeId);

    /**
     * 查询用户素材点赞列表
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 用户素材点赞集合
     */
    public List<UserMaterialLike> selectUserMaterialLikeList(UserMaterialLike userMaterialLike);

    /**
     * 新增用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    public int insertUserMaterialLike(UserMaterialLike userMaterialLike);

    /**
     * 修改用户素材点赞
     * 
     * @param userMaterialLike 用户素材点赞
     * @return 结果
     */
    public int updateUserMaterialLike(UserMaterialLike userMaterialLike);

    /**
     * 删除用户素材点赞
     * 
     * @param likeId 用户素材点赞主键
     * @return 结果
     */
    public int deleteUserMaterialLikeByLikeId(Long likeId);

    /**
     * 批量删除用户素材点赞
     * 
     * @param likeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserMaterialLikeByLikeIds(Long[] likeIds);

    /**
     * 根据用户ID和素材ID删除点赞记录
     * 
     * @param userId 用户ID
     * @param materialId 素材ID
     * @return 结果
     */
    public int deleteByUserIdAndMaterialId(@Param("userId") Long userId, @Param("materialId") String materialId);

    /**
     * 查询用户已点赞的素材ID列表
     * 
     * @param userId 用户ID
     * @param materialIds 素材ID列表
     * @return 已点赞的素材ID列表
     */
    public List<String> selectLikedMaterialIds(@Param("userId") Long userId, @Param("materialIds") List<String> materialIds);

    /**
     * 统计素材的点赞数
     * 
     * @param materialId 素材ID
     * @return 点赞数
     */
    public int countLikesByMaterialId(@Param("materialId") String materialId);
}
