package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExternalSystem;

/**
 * 外部系统配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface ExternalSystemMapper 
{
    /**
     * 查询外部系统配置
     * 
     * @param systemId 外部系统配置主键
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemBySystemId(Long systemId);

    /**
     * 根据API密钥查询外部系统配置
     * 
     * @param apiKey API密钥
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemByApiKey(String apiKey);

    /**
     * 根据系统编码查询外部系统配置
     * 
     * @param systemCode 系统编码
     * @return 外部系统配置
     */
    public ExternalSystem selectExternalSystemBySystemCode(String systemCode);

    /**
     * 查询外部系统配置列表
     * 
     * @param externalSystem 外部系统配置
     * @return 外部系统配置集合
     */
    public List<ExternalSystem> selectExternalSystemList(ExternalSystem externalSystem);

    /**
     * 新增外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    public int insertExternalSystem(ExternalSystem externalSystem);

    /**
     * 修改外部系统配置
     * 
     * @param externalSystem 外部系统配置
     * @return 结果
     */
    public int updateExternalSystem(ExternalSystem externalSystem);

    /**
     * 删除外部系统配置
     * 
     * @param systemId 外部系统配置主键
     * @return 结果
     */
    public int deleteExternalSystemBySystemId(Long systemId);

    /**
     * 批量删除外部系统配置
     * 
     * @param systemIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExternalSystemBySystemIds(Long[] systemIds);

    /**
     * 校验系统编码是否唯一
     * 
     * @param systemCode 系统编码
     * @return 结果
     */
    public ExternalSystem checkSystemCodeUnique(String systemCode);

    /**
     * 校验系统名称是否唯一
     * 
     * @param systemName 系统名称
     * @return 结果
     */
    public ExternalSystem checkSystemNameUnique(String systemName);
}
