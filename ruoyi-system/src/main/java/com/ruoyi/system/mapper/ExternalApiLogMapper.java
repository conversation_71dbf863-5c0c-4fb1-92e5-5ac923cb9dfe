package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExternalApiLog;

/**
 * 外部API调用日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface ExternalApiLogMapper 
{
    /**
     * 查询外部API调用日志
     * 
     * @param logId 外部API调用日志主键
     * @return 外部API调用日志
     */
    public ExternalApiLog selectExternalApiLogByLogId(Long logId);

    /**
     * 查询外部API调用日志列表
     * 
     * @param externalApiLog 外部API调用日志
     * @return 外部API调用日志集合
     */
    public List<ExternalApiLog> selectExternalApiLogList(ExternalApiLog externalApiLog);

    /**
     * 新增外部API调用日志
     * 
     * @param externalApiLog 外部API调用日志
     * @return 结果
     */
    public int insertExternalApiLog(ExternalApiLog externalApiLog);

    /**
     * 修改外部API调用日志
     * 
     * @param externalApiLog 外部API调用日志
     * @return 结果
     */
    public int updateExternalApiLog(ExternalApiLog externalApiLog);

    /**
     * 删除外部API调用日志
     * 
     * @param logId 外部API调用日志主键
     * @return 结果
     */
    public int deleteExternalApiLogByLogId(Long logId);

    /**
     * 批量删除外部API调用日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExternalApiLogByLogIds(Long[] logIds);

    /**
     * 清理指定天数前的日志
     * 
     * @param days 保留天数
     * @return 结果
     */
    public int cleanExternalApiLogByDays(int days);

    /**
     * 统计API调用次数
     * 
     * @param externalApiLog 查询条件
     * @return 调用次数
     */
    public Long countExternalApiLog(ExternalApiLog externalApiLog);

    /**
     * 统计API调用成功率
     * 
     * @param systemId 系统ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率统计
     */
    public List<ExternalApiLog> selectApiSuccessRate(Long systemId, String startTime, String endTime);
}
