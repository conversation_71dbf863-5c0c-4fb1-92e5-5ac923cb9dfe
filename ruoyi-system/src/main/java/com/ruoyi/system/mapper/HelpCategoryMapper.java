package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HelpCategory;

/**
 * 帮助文档分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface HelpCategoryMapper 
{
    /**
     * 查询帮助文档分类
     * 
     * @param id 帮助文档分类主键
     * @return 帮助文档分类
     */
    public HelpCategory selectHelpCategoryById(Long id);

    /**
     * 查询帮助文档分类列表
     * 
     * @param helpCategory 帮助文档分类
     * @return 帮助文档分类集合
     */
    public List<HelpCategory> selectHelpCategoryList(HelpCategory helpCategory);

    /**
     * 新增帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    public int insertHelpCategory(HelpCategory helpCategory);

    /**
     * 修改帮助文档分类
     * 
     * @param helpCategory 帮助文档分类
     * @return 结果
     */
    public int updateHelpCategory(HelpCategory helpCategory);

    /**
     * 删除帮助文档分类
     * 
     * @param id 帮助文档分类主键
     * @return 结果
     */
    public int deleteHelpCategoryById(Long id);

    /**
     * 批量删除帮助文档分类
     * 
     * @param ids 需要删除的帮助文档分类主键集合
     * @return 结果
     */
    public int deleteHelpCategoryByIds(Long[] ids);
}
