package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.HelpDocument;

/**
 * 帮助文档Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface HelpDocumentMapper 
{
    /**
     * 查询帮助文档
     * 
     * @param id 帮助文档主键
     * @return 帮助文档
     */
    public HelpDocument selectHelpDocumentById(Long id);

    /**
     * 查询帮助文档列表
     * 
     * @param helpDocument 帮助文档
     * @return 帮助文档集合
     */
    public List<HelpDocument> selectHelpDocumentList(HelpDocument helpDocument);

    /**
     * 新增帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int insertHelpDocument(HelpDocument helpDocument);

    /**
     * 修改帮助文档
     * 
     * @param helpDocument 帮助文档
     * @return 结果
     */
    public int updateHelpDocument(HelpDocument helpDocument);

    /**
     * 删除帮助文档
     * 
     * @param id 帮助文档主键
     * @return 结果
     */
    public int deleteHelpDocumentById(Long id);

    /**
     * 批量删除帮助文档
     * 
     * @param ids 需要删除的帮助文档主键集合
     * @return 结果
     */
    public int deleteHelpDocumentByIds(Long[] ids);

    /**
     * 搜索帮助文档
     * 
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    public List<HelpDocument> searchDocuments(@Param("keyword") String keyword);

    /**
     * 获取热门文档
     * 
     * @param limit 数量限制
     * @return 热门文档列表
     */
    public List<HelpDocument> getHotDocuments(@Param("limit") Integer limit);

    /**
     * 增加文档查看次数
     * 
     * @param id 文档ID
     * @return 结果
     */
    public int incrementViewCount(@Param("id") Long id);

    /**
     * 获取常见问题（FAQ）
     * 
     * @param limit 数量限制
     * @return FAQ列表
     */
    public List<HelpDocument> getFaqDocuments(@Param("limit") Integer limit);
}
