package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 外部API调用日志对象 external_api_log
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public class ExternalApiLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 系统ID */
    @Excel(name = "系统ID")
    private Long systemId;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 请求URL */
    @Excel(name = "请求URL")
    private String requestUrl;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 请求头 */
    private String requestHeaders;

    /** 请求参数 */
    private String requestParams;

    /** 响应状态码 */
    @Excel(name = "响应状态码")
    private Integer responseStatus;

    /** 响应头 */
    private String responseHeaders;

    /** 响应内容 */
    private String responseBody;

    /** 请求耗时(毫秒) */
    @Excel(name = "请求耗时")
    private Long duration;

    /** 请求IP */
    @Excel(name = "请求IP")
    private String requestIp;

    /** 用户代理 */
    private String userAgent;

    /** 是否成功 */
    @Excel(name = "是否成功", readConverterExp = "0=失败,1=成功")
    private String isSuccess;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 请求时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "请求时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setSystemId(Long systemId) 
    {
        this.systemId = systemId;
    }

    public Long getSystemId() 
    {
        return systemId;
    }
    public void setSystemName(String systemName) 
    {
        this.systemName = systemName;
    }

    public String getSystemName() 
    {
        return systemName;
    }
    public void setRequestUrl(String requestUrl) 
    {
        this.requestUrl = requestUrl;
    }

    public String getRequestUrl() 
    {
        return requestUrl;
    }
    public void setRequestMethod(String requestMethod) 
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() 
    {
        return requestMethod;
    }
    public void setRequestHeaders(String requestHeaders) 
    {
        this.requestHeaders = requestHeaders;
    }

    public String getRequestHeaders() 
    {
        return requestHeaders;
    }
    public void setRequestParams(String requestParams) 
    {
        this.requestParams = requestParams;
    }

    public String getRequestParams() 
    {
        return requestParams;
    }
    public void setResponseStatus(Integer responseStatus) 
    {
        this.responseStatus = responseStatus;
    }

    public Integer getResponseStatus() 
    {
        return responseStatus;
    }
    public void setResponseHeaders(String responseHeaders) 
    {
        this.responseHeaders = responseHeaders;
    }

    public String getResponseHeaders() 
    {
        return responseHeaders;
    }
    public void setResponseBody(String responseBody) 
    {
        this.responseBody = responseBody;
    }

    public String getResponseBody() 
    {
        return responseBody;
    }
    public void setDuration(Long duration) 
    {
        this.duration = duration;
    }

    public Long getDuration() 
    {
        return duration;
    }
    public void setRequestIp(String requestIp) 
    {
        this.requestIp = requestIp;
    }

    public String getRequestIp() 
    {
        return requestIp;
    }
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }
    public void setIsSuccess(String isSuccess) 
    {
        this.isSuccess = isSuccess;
    }

    public String getIsSuccess() 
    {
        return isSuccess;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setRequestTime(Date requestTime) 
    {
        this.requestTime = requestTime;
    }

    public Date getRequestTime() 
    {
        return requestTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("systemId", getSystemId())
            .append("systemName", getSystemName())
            .append("requestUrl", getRequestUrl())
            .append("requestMethod", getRequestMethod())
            .append("requestHeaders", getRequestHeaders())
            .append("requestParams", getRequestParams())
            .append("responseStatus", getResponseStatus())
            .append("responseHeaders", getResponseHeaders())
            .append("responseBody", getResponseBody())
            .append("duration", getDuration())
            .append("requestIp", getRequestIp())
            .append("userAgent", getUserAgent())
            .append("isSuccess", getIsSuccess())
            .append("errorMessage", getErrorMessage())
            .append("requestTime", getRequestTime())
            .toString();
    }
}
