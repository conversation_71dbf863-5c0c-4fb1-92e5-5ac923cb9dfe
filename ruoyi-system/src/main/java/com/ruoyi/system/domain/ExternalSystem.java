package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 外部系统配置对象 external_system
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
public class ExternalSystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 系统ID */
    private Long systemId;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;

    /** API密钥 */
    @Excel(name = "API密钥")
    private String apiKey;

    /** API密钥密文 */
    private String apiSecret;

    /** 系统基础URL */
    @Excel(name = "系统基础URL")
    private String baseUrl;

    /** 超时时间(秒) */
    @Excel(name = "超时时间")
    private Integer timeout;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** IP白名单 */
    @Excel(name = "IP白名单")
    private String allowedIps;

    /** 限流配置(每分钟请求数) */
    @Excel(name = "限流配置")
    private Integer rateLimit;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSystemId(Long systemId) 
    {
        this.systemId = systemId;
    }

    public Long getSystemId() 
    {
        return systemId;
    }
    public void setSystemName(String systemName) 
    {
        this.systemName = systemName;
    }

    public String getSystemName() 
    {
        return systemName;
    }
    public void setSystemCode(String systemCode) 
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode() 
    {
        return systemCode;
    }
    public void setApiKey(String apiKey) 
    {
        this.apiKey = apiKey;
    }

    public String getApiKey() 
    {
        return apiKey;
    }
    public void setApiSecret(String apiSecret) 
    {
        this.apiSecret = apiSecret;
    }

    public String getApiSecret() 
    {
        return apiSecret;
    }
    public void setBaseUrl(String baseUrl) 
    {
        this.baseUrl = baseUrl;
    }

    public String getBaseUrl() 
    {
        return baseUrl;
    }
    public void setTimeout(Integer timeout) 
    {
        this.timeout = timeout;
    }

    public Integer getTimeout() 
    {
        return timeout;
    }
    public void setRetryCount(Integer retryCount) 
    {
        this.retryCount = retryCount;
    }

    public Integer getRetryCount() 
    {
        return retryCount;
    }
    public void setAllowedIps(String allowedIps) 
    {
        this.allowedIps = allowedIps;
    }

    public String getAllowedIps() 
    {
        return allowedIps;
    }
    public void setRateLimit(Integer rateLimit) 
    {
        this.rateLimit = rateLimit;
    }

    public Integer getRateLimit() 
    {
        return rateLimit;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("systemId", getSystemId())
            .append("systemName", getSystemName())
            .append("systemCode", getSystemCode())
            .append("apiKey", getApiKey())
            .append("apiSecret", getApiSecret())
            .append("baseUrl", getBaseUrl())
            .append("timeout", getTimeout())
            .append("retryCount", getRetryCount())
            .append("allowedIps", getAllowedIps())
            .append("rateLimit", getRateLimit())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
