package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户素材点赞对象 user_material_like
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public class UserMaterialLike extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 点赞ID */
    private Long likeId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 素材ID（guiId） */
    @Excel(name = "素材ID")
    private String materialId;

    /** 素材标题 */
    @Excel(name = "素材标题")
    private String materialTitle;

    /** 素材标签 */
    @Excel(name = "素材标签")
    private String materialTag;

    /** 缩略图URL */
    @Excel(name = "缩略图URL")
    private String photoUrl;

    /** 详情图URL */
    @Excel(name = "详情图URL")
    private String detailUrl;

    /** 点赞时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "点赞时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date likeTime;

    public void setLikeId(Long likeId) 
    {
        this.likeId = likeId;
    }

    public Long getLikeId() 
    {
        return likeId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setMaterialId(String materialId) 
    {
        this.materialId = materialId;
    }

    public String getMaterialId() 
    {
        return materialId;
    }
    public void setMaterialTitle(String materialTitle) 
    {
        this.materialTitle = materialTitle;
    }

    public String getMaterialTitle() 
    {
        return materialTitle;
    }
    public void setMaterialTag(String materialTag) 
    {
        this.materialTag = materialTag;
    }

    public String getMaterialTag() 
    {
        return materialTag;
    }
    public void setPhotoUrl(String photoUrl) 
    {
        this.photoUrl = photoUrl;
    }

    public String getPhotoUrl() 
    {
        return photoUrl;
    }
    public void setDetailUrl(String detailUrl) 
    {
        this.detailUrl = detailUrl;
    }

    public String getDetailUrl() 
    {
        return detailUrl;
    }
    public void setLikeTime(Date likeTime) 
    {
        this.likeTime = likeTime;
    }

    public Date getLikeTime() 
    {
        return likeTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("likeId", getLikeId())
            .append("userId", getUserId())
            .append("materialId", getMaterialId())
            .append("materialTitle", getMaterialTitle())
            .append("materialTag", getMaterialTag())
            .append("photoUrl", getPhotoUrl())
            .append("detailUrl", getDetailUrl())
            .append("likeTime", getLikeTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
