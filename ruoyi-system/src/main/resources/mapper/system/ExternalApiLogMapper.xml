<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExternalApiLogMapper">
    
    <resultMap type="ExternalApiLog" id="ExternalApiLogResult">
        <result property="logId"            column="log_id"            />
        <result property="systemId"         column="system_id"         />
        <result property="systemName"       column="system_name"       />
        <result property="requestUrl"       column="request_url"       />
        <result property="requestMethod"    column="request_method"    />
        <result property="requestHeaders"   column="request_headers"   />
        <result property="requestParams"    column="request_params"    />
        <result property="responseStatus"   column="response_status"   />
        <result property="responseHeaders"  column="response_headers"  />
        <result property="responseBody"     column="response_body"     />
        <result property="duration"         column="duration"          />
        <result property="requestIp"        column="request_ip"        />
        <result property="userAgent"        column="user_agent"        />
        <result property="isSuccess"        column="is_success"        />
        <result property="errorMessage"     column="error_message"     />
        <result property="requestTime"      column="request_time"      />
    </resultMap>

    <sql id="selectExternalApiLogVo">
        select log_id, system_id, system_name, request_url, request_method, request_headers, request_params, response_status, response_headers, response_body, duration, request_ip, user_agent, is_success, error_message, request_time from external_api_log
    </sql>

    <select id="selectExternalApiLogList" parameterType="ExternalApiLog" resultMap="ExternalApiLogResult">
        <include refid="selectExternalApiLogVo"/>
        <where>  
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="requestUrl != null  and requestUrl != ''"> and request_url like concat('%', #{requestUrl}, '%')</if>
            <if test="requestMethod != null  and requestMethod != ''"> and request_method = #{requestMethod}</if>
            <if test="responseStatus != null "> and response_status = #{responseStatus}</if>
            <if test="requestIp != null  and requestIp != ''"> and request_ip = #{requestIp}</if>
            <if test="isSuccess != null  and isSuccess != ''"> and is_success = #{isSuccess}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(request_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(request_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by request_time desc
    </select>
    
    <select id="selectExternalApiLogByLogId" parameterType="Long" resultMap="ExternalApiLogResult">
        <include refid="selectExternalApiLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertExternalApiLog" parameterType="ExternalApiLog" useGeneratedKeys="true" keyProperty="logId">
        insert into external_api_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemId != null">system_id,</if>
            <if test="systemName != null and systemName != ''">system_name,</if>
            <if test="requestUrl != null and requestUrl != ''">request_url,</if>
            <if test="requestMethod != null and requestMethod != ''">request_method,</if>
            <if test="requestHeaders != null">request_headers,</if>
            <if test="requestParams != null">request_params,</if>
            <if test="responseStatus != null">response_status,</if>
            <if test="responseHeaders != null">response_headers,</if>
            <if test="responseBody != null">response_body,</if>
            <if test="duration != null">duration,</if>
            <if test="requestIp != null and requestIp != ''">request_ip,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="isSuccess != null and isSuccess != ''">is_success,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="requestTime != null">request_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemId != null">#{systemId},</if>
            <if test="systemName != null and systemName != ''">#{systemName},</if>
            <if test="requestUrl != null and requestUrl != ''">#{requestUrl},</if>
            <if test="requestMethod != null and requestMethod != ''">#{requestMethod},</if>
            <if test="requestHeaders != null">#{requestHeaders},</if>
            <if test="requestParams != null">#{requestParams},</if>
            <if test="responseStatus != null">#{responseStatus},</if>
            <if test="responseHeaders != null">#{responseHeaders},</if>
            <if test="responseBody != null">#{responseBody},</if>
            <if test="duration != null">#{duration},</if>
            <if test="requestIp != null and requestIp != ''">#{requestIp},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="isSuccess != null and isSuccess != ''">#{isSuccess},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="requestTime != null">#{requestTime},</if>
         </trim>
    </insert>

    <update id="updateExternalApiLog" parameterType="ExternalApiLog">
        update external_api_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemId != null">system_id = #{systemId},</if>
            <if test="systemName != null and systemName != ''">system_name = #{systemName},</if>
            <if test="requestUrl != null and requestUrl != ''">request_url = #{requestUrl},</if>
            <if test="requestMethod != null and requestMethod != ''">request_method = #{requestMethod},</if>
            <if test="requestHeaders != null">request_headers = #{requestHeaders},</if>
            <if test="requestParams != null">request_params = #{requestParams},</if>
            <if test="responseStatus != null">response_status = #{responseStatus},</if>
            <if test="responseHeaders != null">response_headers = #{responseHeaders},</if>
            <if test="responseBody != null">response_body = #{responseBody},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="requestIp != null and requestIp != ''">request_ip = #{requestIp},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="isSuccess != null and isSuccess != ''">is_success = #{isSuccess},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="requestTime != null">request_time = #{requestTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteExternalApiLogByLogId" parameterType="Long">
        delete from external_api_log where log_id = #{logId}
    </delete>

    <delete id="deleteExternalApiLogByLogIds" parameterType="String">
        delete from external_api_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="cleanExternalApiLogByDays" parameterType="int">
        delete from external_api_log where request_time &lt; date_sub(now(), interval #{days} day)
    </delete>

    <select id="countExternalApiLog" parameterType="ExternalApiLog" resultType="Long">
        select count(*) from external_api_log
        <where>
            <if test="systemId != null "> and system_id = #{systemId}</if>
            <if test="isSuccess != null  and isSuccess != ''"> and is_success = #{isSuccess}</if>
            <if test="params.beginTime != null and params.beginTime != ''">
                and date_format(request_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                and date_format(request_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectApiSuccessRate" resultMap="ExternalApiLogResult">
        select 
            system_id,
            system_name,
            count(*) as total_count,
            sum(case when is_success = '1' then 1 else 0 end) as success_count,
            round(sum(case when is_success = '1' then 1 else 0 end) * 100.0 / count(*), 2) as success_rate,
            avg(duration) as avg_duration
        from external_api_log
        <where>
            <if test="systemId != null">and system_id = #{systemId}</if>
            <if test="startTime != null and startTime != ''">
                and request_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and request_time &lt;= #{endTime}
            </if>
        </where>
        group by system_id, system_name
        order by success_rate desc
    </select>

</mapper>
