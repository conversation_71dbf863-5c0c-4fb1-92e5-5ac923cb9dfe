<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExternalSystemMapper">
    
    <resultMap type="ExternalSystem" id="ExternalSystemResult">
        <result property="systemId"       column="system_id"       />
        <result property="systemName"     column="system_name"     />
        <result property="systemCode"     column="system_code"     />
        <result property="apiKey"         column="api_key"         />
        <result property="apiSecret"      column="api_secret"      />
        <result property="baseUrl"        column="base_url"        />
        <result property="timeout"        column="timeout"         />
        <result property="retryCount"     column="retry_count"     />
        <result property="allowedIps"     column="allowed_ips"     />
        <result property="rateLimit"      column="rate_limit"      />
        <result property="status"         column="status"          />
        <result property="delFlag"        column="del_flag"        />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
    </resultMap>

    <sql id="selectExternalSystemVo">
        select system_id, system_name, system_code, api_key, api_secret, base_url, timeout, retry_count, allowed_ips, rate_limit, status, del_flag, create_by, create_time, update_by, update_time, remark from external_system
    </sql>

    <select id="selectExternalSystemList" parameterType="ExternalSystem" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        <where>  
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectExternalSystemBySystemId" parameterType="Long" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        where system_id = #{systemId} and del_flag = '0'
    </select>

    <select id="selectExternalSystemByApiKey" parameterType="String" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        where api_key = #{apiKey} and status = '0' and del_flag = '0'
    </select>

    <select id="selectExternalSystemBySystemCode" parameterType="String" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        where system_code = #{systemCode} and del_flag = '0'
    </select>

    <select id="checkSystemCodeUnique" parameterType="String" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        where system_code = #{systemCode} and del_flag = '0' limit 1
    </select>

    <select id="checkSystemNameUnique" parameterType="String" resultMap="ExternalSystemResult">
        <include refid="selectExternalSystemVo"/>
        where system_name = #{systemName} and del_flag = '0' limit 1
    </select>
        
    <insert id="insertExternalSystem" parameterType="ExternalSystem" useGeneratedKeys="true" keyProperty="systemId">
        insert into external_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemName != null and systemName != ''">system_name,</if>
            <if test="systemCode != null and systemCode != ''">system_code,</if>
            <if test="apiKey != null and apiKey != ''">api_key,</if>
            <if test="apiSecret != null and apiSecret != ''">api_secret,</if>
            <if test="baseUrl != null and baseUrl != ''">base_url,</if>
            <if test="timeout != null">timeout,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="allowedIps != null">allowed_ips,</if>
            <if test="rateLimit != null">rate_limit,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemName != null and systemName != ''">#{systemName},</if>
            <if test="systemCode != null and systemCode != ''">#{systemCode},</if>
            <if test="apiKey != null and apiKey != ''">#{apiKey},</if>
            <if test="apiSecret != null and apiSecret != ''">#{apiSecret},</if>
            <if test="baseUrl != null and baseUrl != ''">#{baseUrl},</if>
            <if test="timeout != null">#{timeout},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="allowedIps != null">#{allowedIps},</if>
            <if test="rateLimit != null">#{rateLimit},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExternalSystem" parameterType="ExternalSystem">
        update external_system
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemName != null and systemName != ''">system_name = #{systemName},</if>
            <if test="systemCode != null and systemCode != ''">system_code = #{systemCode},</if>
            <if test="apiKey != null and apiKey != ''">api_key = #{apiKey},</if>
            <if test="apiSecret != null and apiSecret != ''">api_secret = #{apiSecret},</if>
            <if test="baseUrl != null and baseUrl != ''">base_url = #{baseUrl},</if>
            <if test="timeout != null">timeout = #{timeout},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="allowedIps != null">allowed_ips = #{allowedIps},</if>
            <if test="rateLimit != null">rate_limit = #{rateLimit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where system_id = #{systemId}
    </update>

    <delete id="deleteExternalSystemBySystemId" parameterType="Long">
        update external_system set del_flag = '2' where system_id = #{systemId}
    </delete>

    <delete id="deleteExternalSystemBySystemIds" parameterType="String">
        update external_system set del_flag = '2' where system_id in 
        <foreach item="systemId" collection="array" open="(" separator="," close=")">
            #{systemId}
        </foreach>
    </delete>

</mapper>
