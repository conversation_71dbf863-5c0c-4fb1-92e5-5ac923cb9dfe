<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserMaterialLikeMapper">
    
    <resultMap type="UserMaterialLike" id="UserMaterialLikeResult">
        <result property="likeId"    column="like_id"    />
        <result property="userId"    column="user_id"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialTitle"    column="material_title"    />
        <result property="materialTag"    column="material_tag"    />
        <result property="photoUrl"    column="photo_url"    />
        <result property="detailUrl"    column="detail_url"    />
        <result property="likeTime"    column="like_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserMaterialLikeVo">
        select like_id, user_id, material_id, material_title, material_tag, photo_url, detail_url, like_time, create_by, create_time from user_material_like
    </sql>

    <select id="selectUserMaterialLikeList" parameterType="UserMaterialLike" resultMap="UserMaterialLikeResult">
        <include refid="selectUserMaterialLikeVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="materialId != null  and materialId != ''"> and material_id = #{materialId}</if>
            <if test="materialTitle != null  and materialTitle != ''"> and material_title like concat('%', #{materialTitle}, '%')</if>
            <if test="materialTag != null  and materialTag != ''"> and material_tag = #{materialTag}</if>
        </where>
        order by like_time desc
    </select>
    
    <select id="selectUserMaterialLikeByLikeId" parameterType="Long" resultMap="UserMaterialLikeResult">
        <include refid="selectUserMaterialLikeVo"/>
        where like_id = #{likeId}
    </select>

    <select id="selectLikedMaterialIds" resultType="String">
        select material_id from user_material_like 
        where user_id = #{userId} 
        and material_id in
        <foreach item="materialId" collection="materialIds" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </select>

    <select id="countLikesByMaterialId" parameterType="String" resultType="int">
        select count(*) from user_material_like where material_id = #{materialId}
    </select>
        
    <insert id="insertUserMaterialLike" parameterType="UserMaterialLike" useGeneratedKeys="true" keyProperty="likeId">
        insert into user_material_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="materialTitle != null">material_title,</if>
            <if test="materialTag != null">material_tag,</if>
            <if test="photoUrl != null">photo_url,</if>
            <if test="detailUrl != null">detail_url,</if>
            <if test="likeTime != null">like_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="materialTitle != null">#{materialTitle},</if>
            <if test="materialTag != null">#{materialTag},</if>
            <if test="photoUrl != null">#{photoUrl},</if>
            <if test="detailUrl != null">#{detailUrl},</if>
            <if test="likeTime != null">#{likeTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateUserMaterialLike" parameterType="UserMaterialLike">
        update user_material_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="materialTitle != null">material_title = #{materialTitle},</if>
            <if test="materialTag != null">material_tag = #{materialTag},</if>
            <if test="photoUrl != null">photo_url = #{photoUrl},</if>
            <if test="detailUrl != null">detail_url = #{detailUrl},</if>
            <if test="likeTime != null">like_time = #{likeTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where like_id = #{likeId}
    </update>

    <delete id="deleteUserMaterialLikeByLikeId" parameterType="Long">
        delete from user_material_like where like_id = #{likeId}
    </delete>

    <delete id="deleteUserMaterialLikeByLikeIds" parameterType="String">
        delete from user_material_like where like_id in 
        <foreach item="likeId" collection="array" open="(" separator="," close=")">
            #{likeId}
        </foreach>
    </delete>

    <delete id="deleteByUserIdAndMaterialId">
        delete from user_material_like where user_id = #{userId} and material_id = #{materialId}
    </delete>
</mapper>
