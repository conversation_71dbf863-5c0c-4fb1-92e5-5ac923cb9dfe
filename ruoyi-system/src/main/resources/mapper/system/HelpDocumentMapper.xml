<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HelpDocumentMapper">
    
    <resultMap type="HelpDocument" id="HelpDocumentResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="summary"    column="summary"    />
        <result property="keywords"    column="keywords"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="viewCount"    column="view_count"    />
        <result property="isHot"    column="is_hot"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectHelpDocumentVo">
        select d.id, d.category_id, d.title, d.content, d.summary, d.keywords, d.sort_order, 
               d.view_count, d.is_hot, d.status, d.create_by, d.create_time, d.update_by, d.update_time, d.remark,
               c.name as category_name
        from help_document d
        left join help_category c on d.category_id = c.id
    </sql>

    <select id="selectHelpDocumentList" parameterType="HelpDocument" resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        <where>  
            <if test="categoryId != null "> and d.category_id = #{categoryId}</if>
            <if test="title != null  and title != ''"> and d.title like concat('%', #{title}, '%')</if>
            <if test="keywords != null  and keywords != ''"> and d.keywords like concat('%', #{keywords}, '%')</if>
            <if test="isHot != null  and isHot != ''"> and d.is_hot = #{isHot}</if>
            <if test="status != null  and status != ''"> and d.status = #{status}</if>
        </where>
        order by d.sort_order asc, d.id desc
    </select>
    
    <select id="selectHelpDocumentById" parameterType="Long" resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        where d.id = #{id}
    </select>

    <select id="searchDocuments" parameterType="String" resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        where d.status = '0'
        and (d.title like concat('%', #{keyword}, '%') 
             or d.summary like concat('%', #{keyword}, '%')
             or d.keywords like concat('%', #{keyword}, '%')
             or d.content like concat('%', #{keyword}, '%'))
        order by d.view_count desc, d.id desc
    </select>

    <select id="getHotDocuments" parameterType="Integer" resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        where d.status = '0'
        order by d.view_count desc, d.id desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="getFaqDocuments" parameterType="Integer" resultMap="HelpDocumentResult">
        <include refid="selectHelpDocumentVo"/>
        where d.status = '0' and d.is_hot = '1'
        order by d.sort_order asc, d.view_count desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>
        
    <insert id="insertHelpDocument" parameterType="HelpDocument" useGeneratedKeys="true" keyProperty="id">
        insert into help_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="summary != null">summary,</if>
            <if test="keywords != null">keywords,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="summary != null">#{summary},</if>
            <if test="keywords != null">#{keywords},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateHelpDocument" parameterType="HelpDocument">
        update help_document
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="keywords != null">keywords = #{keywords},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="incrementViewCount" parameterType="Long">
        update help_document set view_count = view_count + 1 where id = #{id}
    </update>

    <delete id="deleteHelpDocumentById" parameterType="Long">
        delete from help_document where id = #{id}
    </delete>

    <delete id="deleteHelpDocumentByIds" parameterType="String">
        delete from help_document where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
